<template>
  <iframe src="/portfolio/Relatorio.html" class="w-full h-screen"></iframe>
</template>

<script lang="ts">
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'PortfolioPage',
  components: { },
  setup () {
   return {
    };
  }
});
</script>