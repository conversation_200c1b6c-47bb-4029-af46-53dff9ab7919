<template>
  <q-page
    class="min-h-screen"
    :class="$q.dark.isActive ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900' : 'bg-gradient-to-br from-purple-50 via-white to-indigo-50'"
  >
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
      <div
        :class="
          $q.dark.isActive
            ? 'mx-4 lg:mx-8 flex flex-col lg:flex-row justify-between items-center rounded-3xl bg-gradient-to-r from-slate-800/90 to-purple-800/90 backdrop-blur-sm mt-6 p-8 lg:p-12 shadow-2xl border border-purple-500/20'
            : 'mx-4 lg:mx-8 flex flex-col lg:flex-row justify-between items-center rounded-3xl bg-white/80 backdrop-blur-sm mt-6 p-8 lg:p-12 shadow-2xl border border-purple-200'
        "
      >
        <!-- Content Section -->
        <div
          class="flex flex-col items-center lg:items-start justify-center w-full lg:w-3/5 gap-6"
          v-motion
          :initial="{
            opacity: 0,
            x: -100,
          }"
          :enter="{
            opacity: 1,
            x: 0,
            transition: {
              delay: 300,
              duration: 800,
            },
          }"
        >
          <div class="flex items-center gap-3">
            <q-icon
              name="auto_stories"
              size="2rem"
              :class="$q.dark.isActive ? 'text-purple-400' : 'text-purple-600'"
            />
            <span
              class="text-sm lg:text-xl font-light tracking-wider uppercase"
              :class="$q.dark.isActive ? 'text-purple-300' : 'text-purple-700'"
            >
              Nossa Biblioteca
            </span>
          </div>

          <h1
            class="text-2xl lg:text-4xl xl:text-5xl font-bold leading-tight text-center lg:text-left"
            :class="$q.dark.isActive ? 'text-white' : 'text-gray-800'"
          >
            Descubra seu próximo
            <span class="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
              livro favorito
            </span>
          </h1>

          <p
            class="text-base lg:text-lg leading-relaxed text-center lg:text-left max-w-2xl"
            :class="$q.dark.isActive ? 'text-gray-300' : 'text-gray-600'"
          >
            Explore nossa coleção cuidadosamente selecionada de livros.
            Encontre recomendações personalizadas e adquira seus títulos favoritos
            com facilidade através de nossos parceiros.
          </p>

          <div class="flex flex-col sm:flex-row gap-4 mt-4">
            <q-btn
              class="px-8 py-3 text-base font-medium"
              label="Entre em Contato"
              icon="chat"
              color="purple"
              rounded
              unelevated
              no-caps
            />
            <q-btn
              v-if="isColaborador"
              class="px-8 py-3 text-base font-medium"
              label="Adicionar Livro"
              icon="add"
              color="purple"
              outline
              rounded
              no-caps
              @click="toggleDialogNovoLivro"
            />
          </div>
        </div>

        <!-- Image Section -->
        <div
          class="flex items-center justify-center w-full lg:w-2/5 mt-8 lg:mt-0"
          v-motion
          :initial="{
            opacity: 0,
            x: 100,
            scale: 0.8,
          }"
          :enter="{
            opacity: 1,
            x: 0,
            scale: 1,
            transition: {
              delay: 600,
              duration: 800,
            },
          }"
        >
          <div class="relative">
            <div
              class="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-full blur-3xl opacity-20 animate-pulse"
            ></div>
            <img
              :src="getImageUrl('books_2')"
              alt="Biblioteca"
              class="relative object-contain w-80 lg:w-96 h-auto drop-shadow-2xl"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- Search Section -->
    <div class="mx-4 lg:mx-8 mt-12">
      <div
        v-motion
        :initial="{ opacity: 0, y: 50 }"
        :enter="{ opacity: 1, y: 0, transition: { delay: 900 } }"
        :class="
          $q.dark.isActive
            ? 'bg-slate-800/50 backdrop-blur-sm border border-purple-500/20'
            : 'bg-white/70 backdrop-blur-sm border border-purple-200'
        "
        class="rounded-2xl p-6 shadow-xl"
      >
        <div class="flex flex-col gap-4">
          <div class="flex items-center gap-3">
            <q-icon
              name="search"
              size="1.5rem"
              :class="$q.dark.isActive ? 'text-purple-400' : 'text-purple-600'"
            />
            <h2
              class="text-xl font-semibold"
              :class="$q.dark.isActive ? 'text-white' : 'text-gray-800'"
            >
              Encontre seu livro
            </h2>
          </div>

          <q-form @keydown.enter.prevent="updateBooks" class="w-full">
            <q-input
              v-model="filters.titulo_autor"
              placeholder="Digite o título do livro ou nome do autor..."
              :class="$q.dark.isActive ? 'text-white' : 'text-gray-800'"
              class="text-lg"
              outlined
              rounded
              clearable
              :bg-color="$q.dark.isActive ? 'slate-700' : 'white'"
              :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
            >
              <template v-slot:prepend>
                <q-icon name="search" :color="$q.dark.isActive ? 'purple-400' : 'purple-600'" />
              </template>
              <template v-slot:append>
                <q-btn
                  icon="search"
                  round
                  dense
                  flat
                  :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                  @click="updateBooks"
                />
              </template>
            </q-input>
          </q-form>

          <div v-if="Livros.length" class="flex items-center justify-between">
            <span
              class="text-sm font-medium"
              :class="$q.dark.isActive ? 'text-purple-300' : 'text-purple-700'"
            >
              {{ Livros.length }} {{ Livros.length === 1 ? 'livro encontrado' : 'livros encontrados' }}
            </span>
            <q-chip
              :color="$q.dark.isActive ? 'purple-800' : 'purple-100'"
              :text-color="$q.dark.isActive ? 'purple-200' : 'purple-800'"
              icon="auto_stories"
            >
              {{ Livros.length }}
            </q-chip>
          </div>
        </div>
      </div>
    </div>
    <!-- Books Grid -->
    <div class="mx-4 lg:mx-8 mt-8 mb-12">
      <!-- Loading State -->
      <div
        v-if="loading"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6"
      >
        <div
          v-for="n in 8"
          :key="n"
          class="animate-pulse"
        >
          <div
            :class="
              $q.dark.isActive
                ? 'bg-slate-800/50'
                : 'bg-gray-200'
            "
            class="aspect-[3/4] rounded-2xl mb-4"
          ></div>
          <div
            :class="
              $q.dark.isActive
                ? 'bg-slate-700/50'
                : 'bg-gray-200'
            "
            class="h-4 rounded mb-2"
          ></div>
          <div
            :class="
              $q.dark.isActive
                ? 'bg-slate-700/50'
                : 'bg-gray-200'
            "
            class="h-3 rounded w-3/4"
          ></div>
        </div>
      </div>

      <!-- Empty State -->
      <div
        v-else-if="Livros.length === 0 && filters.titulo_autor && !loading"
        class="text-center py-16"
        v-motion
        :initial="{ opacity: 0, scale: 0.9 }"
        :enter="{ opacity: 1, scale: 1, transition: { delay: 1000 } }"
      >
        <q-icon
          name="search_off"
          size="4rem"
          :class="$q.dark.isActive ? 'text-gray-600' : 'text-gray-400'"
        />
        <h3
          class="text-xl font-medium mt-4"
          :class="$q.dark.isActive ? 'text-gray-400' : 'text-gray-600'"
        >
          Nenhum livro encontrado
        </h3>
        <p
          class="text-sm mt-2"
          :class="$q.dark.isActive ? 'text-gray-500' : 'text-gray-500'"
        >
          Tente buscar com outros termos
        </p>
      </div>

      <!-- Books Grid -->
      <div
        v-else-if="Livros.length > 0 && !loading"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 grid-responsive"
      >
        <div
          v-for="(book, index) in Livros"
          :key="book.id"
          v-motion
          :initial="{ opacity: 0, y: 50, scale: 0.9 }"
          :enter="{
            opacity: 1,
            y: 0,
            scale: 1,
            transition: {
              delay: 1000 + (index * 100),
              duration: 500
            }
          }"
          class="group"
        >
          <q-card
            :class="
              $q.dark.isActive
                ? 'bg-slate-800/70 border border-purple-500/20 hover:border-purple-400/60 hover:shadow-purple-400/20'
                : 'bg-white border border-purple-200 hover:border-purple-400/60 hover:shadow-purple-400/20'
            "
            class="h-full cursor-pointer transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 rounded-2xl overflow-hidden relative"
            @mouseover="book.showEditIcon = true"
            @mouseleave="book.showEditIcon = false"
          >
            <!-- Shopping indicator -->
            <div
              class="absolute top-3 left-3 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              <q-chip
                :color="$q.dark.isActive ? 'purple-800' : 'purple-100'"
                :text-color="$q.dark.isActive ? 'purple-200' : 'purple-800'"
                icon="shopping_cart"
                size="sm"
                class="shadow-lg"
              >
                Clique para comprar
              </q-chip>
            </div>
            <!-- Book Cover -->
            <div class="relative aspect-[3/4] overflow-hidden">
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
              ></div>

              <!-- Edit Icon -->
              <div
                class="absolute top-3 right-3 z-20 transition-all duration-300"
                v-show="isColaborador"
              >
                <q-btn
                  icon="edit"
                  round
                  size="md"
                  :color="$q.dark.isActive ? 'orange-400' : 'orange-500'"
                  class="shadow-lg transform hover:scale-110 transition-all duration-200"
                  :class="book.showEditIcon ? 'opacity-100 scale-100' : 'opacity-70 scale-90'"
                  @click="onClickEditar(book.id)"
                >
                  <q-tooltip class="text-sm">Editar livro</q-tooltip>
                </q-btn>
              </div>

              <!-- Buy Button Overlay -->
              <div
                class="absolute inset-0 flex flex-col items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-all duration-300 gap-3"
              >
                <q-btn
                  icon="shopping_cart"
                  label="COMPRAR AGORA"
                  size="lg"
                  color="purple"
                  class="shadow-2xl transform hover:scale-110 transition-all duration-200 font-bold text-white px-6 py-3"
                  style="border: 2px solid #ffffff40; background: linear-gradient(45deg, #8b5cf6, #7c3aed) !important;"
                  rounded
                  no-caps
                  @click="onClickLink(book.id)"
                >
                  <q-tooltip class="text-sm">Comprar este livro</q-tooltip>
                </q-btn>

                <!-- Copy link button (smaller, secondary) -->
                <q-btn
                  icon="content_copy"
                  label="Copiar Link"
                  size="sm"
                  flat
                  :color="$q.dark.isActive ? 'white' : 'white'"
                  class="shadow-lg transform hover:scale-105 transition-all duration-200 backdrop-blur-sm bg-white/20"
                  rounded
                  @click="onClickCopiarLink(book.id)"
                >
                  <q-tooltip class="text-sm">Copiar link do livro</q-tooltip>
                </q-btn>
              </div>

              <img
                :src="book.url_imagem"
                :alt="book.titulo"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
            </div>

            <!-- Book Info -->
            <q-card-section class="p-4 flex-1 flex flex-col">
              <h3
                class="font-semibold text-base leading-tight mb-2 line-clamp-2"
                :class="$q.dark.isActive ? 'text-white' : 'text-gray-800'"
              >
                {{ book.titulo }}
                <q-tooltip class="text-sm">{{ book.titulo }}</q-tooltip>
              </h3>

              <div class="space-y-1 text-sm flex-1">
                <p
                  class="flex items-center gap-2"
                  :class="$q.dark.isActive ? 'text-gray-300' : 'text-gray-600'"
                >
                  <q-icon name="person" size="xs" />
                  {{ book.autor }}
                </p>
                <p
                  class="flex items-center gap-2"
                  :class="$q.dark.isActive ? 'text-gray-300' : 'text-gray-600'"
                >
                  <q-icon name="business" size="xs" />
                  {{ book.editora }}
                </p>
                <p
                  class="flex items-center gap-2"
                  :class="$q.dark.isActive ? 'text-gray-300' : 'text-gray-600'"
                >
                  <q-icon name="calendar_today" size="xs" />
                  {{ formatDateApenasDataComAno(book.dt_publicacao) }}
                </p>
              </div>
            </q-card-section>


          </q-card>
        </div>
      </div>
    </div>


    <!-- Modal para Adicionar/Editar Livro -->
    <q-dialog
      v-model="dialogNovoLivro"
      transition-show="scale"
      transition-hide="scale"
      maximized
      class="z-50"
    >
      <q-card
        :class="
          $q.dark.isActive
            ? 'bg-slate-900 text-white'
            : 'bg-white text-gray-800'
        "
        class="w-full max-w-4xl mx-auto"
      >
        <!-- Header -->
        <q-card-section class="flex items-center justify-between p-6 border-b border-purple-200 dark:border-purple-800">
          <div class="flex items-center gap-3">
            <q-icon
              name="auto_stories"
              size="2rem"
              :class="$q.dark.isActive ? 'text-purple-400' : 'text-purple-600'"
            />
            <h2 class="text-2xl font-bold">
              {{ LivroSelecionado.id ? 'Editar Livro' : 'Adicionar Novo Livro' }}
            </h2>
          </div>
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            :color="$q.dark.isActive ? 'gray-400' : 'gray-600'"
          />
        </q-card-section>

        <!-- Content -->
        <q-card-section class="p-6 max-h-[calc(100vh-200px)] overflow-y-auto">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Form Fields -->
            <div class="space-y-6">
              <q-input
                v-model="LivroSelecionado.titulo"
                label="Título do Livro"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
                class="text-base"
              >
                <template v-slot:prepend>
                  <q-icon name="title" />
                </template>
              </q-input>

              <q-input
                v-model="LivroSelecionado.autor"
                label="Autor"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="person" />
                </template>
              </q-input>

              <q-input
                v-model="LivroSelecionado.editora"
                label="Editora"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="business" />
                </template>
              </q-input>

              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <q-select
                  v-model="LivroSelecionado.idioma"
                  :options="optionsIdioma"
                  label="Idioma"
                  outlined
                  emit-value
                  map-options
                  :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                  :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
                >
                  <template v-slot:prepend>
                    <q-icon name="language" />
                  </template>
                </q-select>

                <q-input
                  v-model.number="LivroSelecionado.nro_paginas"
                  label="Número de Páginas"
                  type="number"
                  outlined
                  :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                  :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
                >
                  <template v-slot:prepend>
                    <q-icon name="menu_book" />
                  </template>
                </q-input>
              </div>

              <q-input
                v-model="LivroSelecionado.dt_publicacao_edit"
                label="Data de Publicação"
                mask="##/##/####"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="calendar_today" />
                </template>
              </q-input>

              <q-input
                v-model="LivroSelecionado.link"
                label="Link de Compra"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="link" />
                </template>
              </q-input>

              <q-input
                v-model="LivroSelecionado.resumo"
                label="Resumo"
                type="textarea"
                rows="4"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="description" />
                </template>
              </q-input>

              <q-input
                v-model="LivroSelecionado.url_imagem"
                label="URL da Capa"
                outlined
                :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
                :bg-color="$q.dark.isActive ? 'slate-800' : 'white'"
              >
                <template v-slot:prepend>
                  <q-icon name="image" />
                </template>
              </q-input>
            </div>

            <!-- Preview -->
            <div class="flex flex-col items-center justify-start">
              <h3
                class="text-lg font-semibold mb-4"
                :class="$q.dark.isActive ? 'text-purple-300' : 'text-purple-700'"
              >
                Preview da Capa
              </h3>
              <div
                :class="
                  $q.dark.isActive
                    ? 'bg-slate-800 border-slate-700'
                    : 'bg-gray-50 border-gray-200'
                "
                class="w-64 aspect-[3/4] border-2 border-dashed rounded-lg flex items-center justify-center overflow-hidden"
              >
                <img
                  v-if="LivroSelecionado.url_imagem"
                  :src="LivroSelecionado.url_imagem"
                  alt="Preview da capa"
                  class="w-full h-full object-cover rounded-lg"
                />
                <div
                  v-else
                  class="text-center p-4"
                  :class="$q.dark.isActive ? 'text-gray-500' : 'text-gray-400'"
                >
                  <q-icon name="image" size="3rem" class="mb-2" />
                  <p class="text-sm">Adicione uma URL para ver a preview</p>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- Actions -->
        <q-card-actions class="p-6 border-t border-purple-200 dark:border-purple-800 flex justify-end gap-3">
          <q-btn
            label="Cancelar"
            flat
            :color="$q.dark.isActive ? 'gray-400' : 'gray-600'"
            class="px-6"
            v-close-popup
          />
          <q-btn
            :label="LivroSelecionado.id ? 'Atualizar' : 'Adicionar'"
            :color="$q.dark.isActive ? 'purple-400' : 'purple-600'"
            unelevated
            class="px-8"
            @click="onClickSave"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts">
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as BookService from 'src/services/BookService';
import { formatDateApenasDataComAno } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { getImageUrl } from 'src/utils/image';

type Livro = {
  id: string;
  titulo: string;
  idioma: string;
  dt_publicacao: string;
  dt_publicacao_edit: string;
  nro_paginas: number;
  autor: string;
  resumo: string;
  editora: string;
  link: string;
  url_imagem: string;
};

export default defineComponent({
  name: 'BibliotecaPage',
  setup() {
    const $q = useQuasar();
    const user = useUserStore();
    const User = ref<UserType>();
    const isColaborador = ref(false)

    const dialogNovoLivro = ref(false);

    const LivroSelecionado = ref({
      id: null,
      titulo: '',
      idioma: 'ptBR',
      dt_publicacao: '',
      dt_publicacao_edit: '',
      nro_paginas: null,
      autor: '',
      resumo: '',
      editora: '',
      link: '',
      url_imagem: '',
    });

    const filters = ref({
      titulo_autor: '',
      // offset: pagination.value.page,
      // limit: pagination.value.rowsPerPage,
    });

    const Livros = ref<Livro[]>([]);
    const loading = ref(false);
    const optionsIdioma = ref([
      { label: 'Português Brasileiro', value: 'ptBR' },
      { label: 'Inglês Americano', value: 'enUS' },
    ]);

    watch(
      () => user.userData,
      (newValue: UserType) => {
        User.value = newValue;
        isColaborador.value = newValue && (newValue.type == 'A' || newValue.type == 'C')
      }
    );

    onMounted(async () => {
      User.value = await user.storeUserDataGetter;
      isColaborador.value = User.value && (User.value.type == 'A' || User.value.type == 'C')
      console.log('🤬 ~ onMounted ~ isColaborador.value:', isColaborador.value)
      updateBooks();
    });

    const updateBooks = async () => {
      loading.value = true;
      try {
        const Books = await BookService.Get(filters.value.titulo_autor);
        Livros.value = Books.data;
      } catch (error) {
        console.error('Erro ao carregar livros:', error);
      } finally {
        loading.value = false;
      }
    };

    const toggleDialogNovoLivro = () => {
      dialogNovoLivro.value = true;
      LivroSelecionado.value = {
        id: null,
        titulo: '',
        idioma: 'ptBR',
        dt_publicacao: '',
        dt_publicacao_edit: '',
        nro_paginas: null,
        autor: '',
        resumo: '',
        editora: '',
        link: '',
        url_imagem: '',
      };
    };

    const onClickSave = async () => {
      $q.loading.show();
      try {
        if (LivroSelecionado.value.id) {
          const response = await BookService.Update(LivroSelecionado.value);
          if (axios.isAxiosError(response)) {
            triggerNegative(
              response.response?.data?.message || 'Erro ao atualizar o livro'
            );
            return;
          }
          triggerSuccess('Livro atualizado');
          dialogNovoLivro.value = false;
          updateBooks();
        } else {
          const response = await BookService.Insert(LivroSelecionado.value);
          if (axios.isAxiosError(response)) {
            triggerNegative(
              response.response?.data?.message || 'Erro ao inserir o livro'
            );
            return;
          }
          triggerSuccess('Livro inserido');
          dialogNovoLivro.value = false;
          updateBooks();
        }
      } catch (error) {
        console.error(error);
      } finally {
        $q.loading.hide();
      }
    };

    const onClickEditar = async (id: string) => {
      const response = await BookService.GetOne(id);
      LivroSelecionado.value = response.data;
      LivroSelecionado.value.dt_publicacao_edit = LivroSelecionado.value
        .dt_publicacao
        ? formatDateApenasDataComAno(LivroSelecionado.value.dt_publicacao)
        : '';
      dialogNovoLivro.value = true;
    };

    const openLink = (link: string) => {
      window.open(link, '_blank');
    }
    const onClickLink = async (id: string) => {
      const response = await BookService.GetLink(id);
      openLink(response);
    };

    const onClickCopiarLink = async (id: string) => {
      const url = await BookService.GetLink(id);
      navigator.clipboard.writeText(url).then(function() {
        triggerSuccess('Link copiado com sucesso!');
      }, function(err) {
        console.error('Could not copy text: ', err);
      });
      return
    };

    return {
      getImageUrl,
      User,
      Livros,
      dialogNovoLivro,
      LivroSelecionado,
      formatDateApenasDataComAno,
      toggleDialogNovoLivro,
      optionsIdioma,
      onClickSave,
      onClickEditar,
      openLink,
      onClickLink,
      onClickCopiarLink,
      updateBooks,
      filters,
      isColaborador,
      loading,
    };
  },
});
</script>

<style scoped>
/* Animações customizadas */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(147, 51, 234, 0.3); }
  50% { box-shadow: 0 0 40px rgba(147, 51, 234, 0.6); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Truncate text para múltiplas linhas */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Efeitos de hover personalizados */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* Gradientes customizados */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Backdrop blur personalizado */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Scrollbar personalizada */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.7);
}

/* Transições suaves */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Sombras personalizadas */
.shadow-book {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(147, 51, 234, 0.1);
}

.shadow-book:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(147, 51, 234, 0.2);
}

/* Estados de loading */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Responsividade aprimorada */
@media (max-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (min-width: 1025px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}
</style>
