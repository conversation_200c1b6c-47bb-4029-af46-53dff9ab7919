<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3">
      <div class="row p-3">
        <q-card class="col">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Total de Acessos: {{total}}</div>
        </q-card-section>
      </q-card>
      </div>
      <div class="p-3">
        <GMapMap :center="center" :zoom="6" map-type-id="terrain" class="h-screen">
          <GMapCluster>
          <GMapMarker
              :key="index"
              v-for="(m, index) in markers"
              :position="m.position"
              :clickable="true"
              :draggable="false"
              @click="center = m.position"
            />
          </GMapCluster>
          <!-- <GMapCluster :zoomOnClick="true">
            
          </GMapCluster> -->
        </GMapMap>
      </div>
    </div>
  </q-page>
</template>

<script lang="ts">
// import QRious from 'qrious';
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';

export default defineComponent({
  name: 'DashboardPage',
  components: {  },
  setup () {
    const user = useUserStore();
    const User = ref<UserType>()
    const markers = ref([
      {
        position: {
          lat: -28.366136250673932,
          lng:  -54.266992761911396,
        },
      }
    ])
    const center = { lat: -28.366136250673932, lng: -54.266992761911396 };
    
    const total = ref(0)

    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    onMounted(async () => {
      const responseAcessos = await UserService.GetUser('acessos')
      const markersAux = []
      if (responseAcessos.data.success === true){
        for (let acesso of responseAcessos.data.acessos){
          let found = markersAux.find((marker) => marker.position.lat === acesso.latitude && marker.position.lng === acesso.longitude)
          total.value += acesso.quantidade
          if (!found) {
            markersAux.push({
                position: {
                  lat: acesso.latitude,
                  lng: acesso.longitude,
                }
            })
          }
        }
      }

      markers.value = markersAux      
    })

    return { 
      User,
      markers,
      total,
      center,
     };


  }
});
</script>

<style scoped>

</style>