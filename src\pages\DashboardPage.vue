<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3">
      <div class="row p-3 items-center">
        <q-card class="col">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Total de Acessos: {{total}}</div>
        </q-card-section>
      </q-card>
      <div class="q-ml-md" v-if="isColaborador">
        <q-btn
          rounded
          color="secondary"
          icon="delivery_dining"
          label="Entregar Análise"
          @click="onClickEntregarAnalise"
        />
      </div>
      </div>
      <div class="p-3">
        <!-- <GMapMap :center="center" :zoom="6" map-type-id="terrain" class="h-screen">
          <GMapCluster>
          <GMapMarker
              :key="index"
              v-for="(m, index) in markers"
              :position="m.position"
              :clickable="true"
              :draggable="false"
              @click="center = m.position"
            />
          </GMapCluster>
          <GMapCluster :zoomOnClick="true">

          </GMapCluster>
        </GMapMap> -->
      </div>
    </div>

    <!-- Modal de Entrega -->
    <q-dialog v-model="dialogEntrega" persistent>
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Entregar Análise</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-form @submit.prevent="onSubmitEntrega" class="q-gutter-md">
            <q-input
              v-model="entregaData.url"
              label="Link da Entrega *"
              outlined
              dense
              :rules="[val => !!val || 'Link é obrigatório', val => isValidUrl(val) || 'URL inválida']"
            >
              <template v-slot:prepend>
                <q-icon name="link" />
              </template>
            </q-input>

            <q-input
              v-model="entregaData.customer_name"
              label="Nome do Cliente *"
              outlined
              dense
              :rules="[val => !!val || 'Nome do cliente é obrigatório']"
            >
              <template v-slot:prepend>
                <q-icon name="person" />
              </template>
            </q-input>
          </q-form>
        </q-card-section>

        <q-card-actions align="right" class="text-primary">
          <q-btn flat label="Cancelar" @click="dialogEntrega = false" />
          <q-btn flat label="Entregar" @click="onSubmitEntrega" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts">
// import QRious from 'qrious';
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';
import * as EntregaService from 'src/services/EntregaService';
import { useQuasar } from 'quasar';
import { triggerSuccess, triggerNegative } from 'src/utils/triggers';
import axios from 'axios';

export default defineComponent({
  name: 'DashboardPage',
  components: {  },
  setup () {
    const $q = useQuasar();
    const user = useUserStore();
    const User = ref<UserType>()
    const isColaborador = ref(false);
    const dialogEntrega = ref(false);
    const entregaData = ref({
      url: '',
      customer_name: ''
    });

    const markers = ref([
      {
        position: {
          lat: -28.366136250673932,
          lng:  -54.266992761911396,
        },
      }
    ])
    const center = { lat: -28.366136250673932, lng: -54.266992761911396 };

    const total = ref(0)

    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
      isColaborador.value = newValue && (newValue.type === 'A' || newValue.type === 'C')
    })

    const onClickEntregarAnalise = () => {
      dialogEntrega.value = true;
    }

    const isValidUrl = (url: string) => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    }

    const onSubmitEntrega = async () => {
      if (!entregaData.value.url || !entregaData.value.customer_name) {
        triggerNegative('Todos os campos são obrigatórios');
        return;
      }

      if (!isValidUrl(entregaData.value.url)) {
        triggerNegative('URL inválida');
        return;
      }

      $q.loading.show({ message: 'Realizando entrega...' });

      try {
        const response = await EntregaService.Entregar(entregaData.value);

        if (axios.isAxiosError(response)) {
          triggerNegative(response.response?.data?.message || 'Erro ao realizar entrega');
        } else {
          triggerSuccess('Entrega realizada com sucesso!');
          dialogEntrega.value = false;
          entregaData.value = { url: '', customer_name: '' };
        }
      } catch (error) {
        console.error('Erro na entrega:', error);
        triggerNegative('Erro ao realizar entrega');
      } finally {
        $q.loading.hide();
      }
    }

    onMounted(async () => {
      User.value = await user.storeUserDataGetter;
      isColaborador.value = User.value && (User.value.type === 'A' || User.value.type === 'C');

      const responseAcessos = await UserService.GetUser('acessos')
      const markersAux = []
      if (responseAcessos.data.success === true){
        for (let acesso of responseAcessos.data.acessos){
          let found = markersAux.find((marker) => marker.position.lat === acesso.latitude && marker.position.lng === acesso.longitude)
          total.value += acesso.quantidade
          if (!found) {
            markersAux.push({
                position: {
                  lat: acesso.latitude,
                  lng: acesso.longitude,
                }
            })
          }
        }
      }

      markers.value = markersAux
    })

    return {
      User,
      markers,
      total,
      center,
      isColaborador,
      dialogEntrega,
      entregaData,
      onClickEntregarAnalise,
      isValidUrl,
      onSubmitEntrega,
     };


  }
});
</script>

<style scoped>

</style>