import TimeAgo from 'javascript-time-ago'
import en from 'javascript-time-ago/locale/en'
TimeAgo.addDefaultLocale(en)

export const formatDate = (originalDate: string) => {
  const dataObjeto = new Date(originalDate);
  const dia = dataObjeto.getUTCDate();
  const mes = dataObjeto.getUTCMonth() + 1;
  const hora = dataObjeto.getUTCHours();
  const minuto = dataObjeto.getUTCMinutes();
  const novaString = `${dia < 10 ? '0' : ''}${dia}/${mes < 10 ? '0' : ''}${mes} ${hora}:${minuto}`;
  return novaString
};

export const formatDateApenasData = (originalDate: string) => {
  const dataObjeto = new Date(originalDate);
  const dia = dataObjeto.getUTCDate();
  const mes = dataObjeto.getUTCMonth() + 1;
  const novaString = `${dia < 10 ? '0' : ''}${dia}/${mes < 10 ? '0' : ''}${mes}`;
  return novaString
}

export const formatDateApenasDataComAno = (originalDate: string) => {
  const dataObjeto = new Date(originalDate);
  const dia = dataObjeto.getUTCDate();
  const mes = dataObjeto.getUTCMonth() + 1;
  const ano = dataObjeto.getUTCFullYear();
  const novaString = `${dia < 10 ? '0' : ''}${dia}/${mes < 10 ? '0' : ''}${mes}/${ano}`;
  return novaString
}

export const generateDateFromApenasDataComAno = (dateString: string) => {
  const [dia, mes, ano] = dateString.split('/').map(Number);
  return new Date(ano, mes - 1, dia).toISOString();
}
export const formatDateChat = async (date: string) => {
  const Data = date === '' ? new Date() : new Date(date)
  const timeAgo = new TimeAgo('en-US')
  return timeAgo.format(Data)
}