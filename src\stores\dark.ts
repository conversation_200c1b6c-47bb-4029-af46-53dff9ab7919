import { defineStore } from 'pinia';
import { DARK_STORAGE } from 'src/stores/storageConfig';


const getDarkStorage = () => {
  const response = localStorage.getItem(DARK_STORAGE);
  const data: boolean = response
    ? JSON.parse(response)
    : false;
  return data;
};

export const useDarkStorage = defineStore(DARK_STORAGE, {
  state: () => ({
    darkData: getDarkStorage(),
  }),
  getters: {
    storeDarkGetter(state) {
      return state.darkData;
    },
  },
  actions: {
    storageDarkSave(newDarkData: boolean) {
      this.darkData = newDarkData;
      localStorage.setItem(DARK_STORAGE, JSON.stringify(this.darkData));
    },
    storageDarkRemove() {
      this.darkData = false;
      localStorage.removeItem(DARK_STORAGE);
    },
  },
});
