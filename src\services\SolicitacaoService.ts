import { instance } from 'src/services/Interceptor/interceptor';
import { environment } from 'src/environment/environment';

export function Get() {
  return instance.get(`${environment.baseUrl}/solicitacao`);
}

export function GetOne(id: string) {
  return instance.get(`${environment.baseUrl}/solicitacao/${id}`);
}

export function Criar(Solicitacao: any) {
  return instance.post(`${environment.baseUrl}/solicitacao`, Solicitacao);
}

export function Alterar(Solicitacao: any) {
  return instance.patch(`${environment.baseUrl}/solicitacao/${Solicitacao.id}`, Solicitacao);
}

export function Apropriar(id: string) {
  return instance.patch(`${environment.baseUrl}/solicitacao/apropriar/${id}`);
}

export function Reuniao(id: string) {
  return instance.patch(`${environment.baseUrl}/solicitacao/reuniao/${id}`);
}

export function Cancelar(id: string) {
  return instance.patch(`${environment.baseUrl}/solicitacao/cancelar/${id}`);
}

export function Orcar(Orcamento: any) {
  return instance.patch(`${environment.baseUrl}/solicitacao/orcar/${Orcamento.id}`, Orcamento);
}

export function Entrega(Orcamento: any) {
  return instance.patch(`${environment.baseUrl}/solicitacao/entrega/${Orcamento.id}`, Orcamento);
}

export function Reabrir(id: string) {
  return instance.patch(`${environment.baseUrl}/solicitacao/reabrir/${id}`);
}
