<template>
  <q-page
    class="w-full h-full"
    :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'"
  >
    <div
      class="container flex flex-col items-center justify-start gap-3 py-5 h-full"
    >
    <q-card style="width: 70vw" class="mt-5">
      <q-card-section>
        <div class="text-h6">Cadastro</div>
      </q-card-section>
      <q-card-section>
        <q-form @submit.prevent="onClickSubmitRegister" class="q-gutter-md" >
          <q-input v-model="registerData.email" :label="'E-mail (será sua forma de realizar o Login)'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="mail" size="15px" />
            </template>
          </q-input>

          <q-input v-model="registerData.fullname" :label="'Nome'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="person" size="15px" />
            </template>
          </q-input>

          <q-input v-model="registerData.cellphone_number" :label="'Telefone'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="phone" size="15px" />
            </template>
          </q-input>

          <q-input v-model="registerData.password" :label="'Senha'" :type="isPwd ? 'password' : 'text'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="lock" size="15px" />
            </template>
            <template v-slot:append>
              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" size="15px" />
            </template>
          </q-input>

          <q-input v-model="registerData.confirmarPassword" :label="'Senha'" :type="isPwd ? 'password' : 'text'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="lock" size="15px" />
            </template>
            <template v-slot:append>
              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" size="15px" />
            </template>
          </q-input>
        </q-form>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          push
          color="primary"
          text-color="white"
          :label="'Finalizar Cadastro'"
          no-caps
          @click="onClickSubmitRegister"
        />
      </q-card-actions>
    </q-card>
    </div>
  </q-page>
</template>

<script lang="ts">

import { UserType, useUserStore } from 'src/stores/user';
import { useRoute, useRouter } from 'vue-router';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';
import * as AuthService from 'src/services/AuthService'
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';

import { initialEdges, initialNodes } from 'src/utils/initialElements';

export default defineComponent({
  name: 'SolicitacoesPage',
  setup() {
    const $q = useQuasar();
    const user = useUserStore();
    const router = useRouter();
    const User = ref<UserType>();
    const isColaborador = ref(false);

    const registerData = ref({ 
      fullname: '',
      cellphone_number: '',
      email: '',
      password: '',
      confirmarPassword: '',
     })

    const isPwd = ref(true)

    watch(
      () => user.userData,
      (newValue: UserType) => {
        User.value = newValue;
      }
    );

    onMounted(async () => {
      User.value = await user.storeUserDataGetter;
      isColaborador.value = User.value.type == 'C' || User.value.type == 'A'

      if (!isColaborador.value) {
        router.push('/');
      }
    });

    const onClickVoltar = () => {
      router.push('/usuarios');
    };


    const onClickSubmitRegister = async () => {

      if (registerData.value.password != registerData.value.confirmarPassword) {
        $q.loading.hide();
        triggerNegative('Senhas não coincidem');
      }
      $q.loading.show();
      try {
        const response = await AuthService.Register({...registerData.value})
        if (axios.isAxiosError(response)) {
          if (response.response){
            triggerNegative(response.response.data.message);
          } else {
            triggerNegative('Houve um erro ao fazer o Cadastro');
          }
          return
        }

        triggerSuccess('Conta criada com sucesso!');
        onClickVoltar();
      } catch (error) {
        console.log(error)
        throw error;
      } finally {
        $q.loading.hide();
      }
        
      };

    return {
      User,
      formatDate,
      formatDateApenasData,
      router,
      isColaborador,
      onClickVoltar,
      onClickSubmitRegister,
      registerData,
      isPwd
    };
  },
});
</script>

<style>
.etapa_atual {
  background: rgb(153 0 204);
  color: white;
  border: 1px solid rgb(153 0 204);
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgb(153 0 204);
  padding: 8px;
}
</style>
