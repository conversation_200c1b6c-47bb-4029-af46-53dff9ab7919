import { instance } from 'src/services/Interceptor/interceptor';
import { environment } from 'src/environment/environment';
import { Book } from '../types/interfaces/IBook';
import { generateDateFromApenasDataComAno } from 'src/utils/dates';

export function Get(titulo_autor?: string) {
  if (titulo_autor) {
    return instance.get(`${environment.baseUrl}/books/?titulo_autor=${titulo_autor}`);
  }
  return instance.get(`${environment.baseUrl}/books`);
}

export function GetOne(id: string) {
  return instance.get(`${environment.baseUrl}/books/${id}`);
}

export function GetLink(id: string) {
  return `${environment.baseUrl}/books/${id}/link`;
}

export function RedirectLink(id: string) {
  return instance.get(`${environment.baseUrl}/books/${id}/link`);
}

export function Insert(data: Book) {
  data.dt_publicacao = generateDateFromApenasDataComAno(data.dt_publicacao_edit);
  Object.keys(data).forEach((key) => { if (key === 'dt_publicacao_edit') delete data[key] })
  return instance.post(`${environment.baseUrl}/books`, data);
}

export function Update(data: Book) {
  data.dt_publicacao = generateDateFromApenasDataComAno(data.dt_publicacao_edit);
  Object.keys(data).forEach((key) => { if (key === 'dt_publicacao_edit') delete data[key] })
  return instance.put(`${environment.baseUrl}/books/${data.id}`, data);
}

export function Delete(data: Book) {
  return instance.delete(`${environment.baseUrl}/books/${data.id}`);
}
