// app global css in SCSS form
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.33.5/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.33.5/dist/theme-default.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/controls@latest/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/minimap@latest/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/node-resizer@latest/dist/style.css';


:root {
  --color-dark-accent: #29304d;
  --color-light-gray: #f5f6f8;
  --color-dark-gray: #c4c4c4;

  --color-light-green: rgb(220 252 231); //green-100
  --color-medium-green: rgb(134 239 172); //green-300
  --color-green: rgb(21 128 61); //green-700
  --color-dark-green: rgb(20 83 45); //green-900

  --color-light-red: rgb(254 226 226); //red-100
  --color-medium-red: rgb(252 165 165); //red-300
  --color-red: rgb(185 28 28); //red-700
  --color-dark-red: rgb(127 29 29); //red-900

  --color-light-yellow: rgb(254 249 195); //yellow-100
  --color-medium-yellow: rgb(253 224 71); //yellow-300
  --color-yellow: rgb(234 179 8); //yellow-500
  // --color-dark-yellow: rgb(127 29 29); //yellow-900
  --scrollbar-width-height: 10px;
  --scrollbar-track: #eeeeee;
  --scrollbar-thumb: rgba(255, 122, 15, 0.9);
  --scrollbar-track-dark: #eeeeee;
  --scrollbar-thumb-dark: rgba(255, 122, 15, 0.9);
}

@font-face {
  font-family: 'League';
  src: local('League'),
    url(../assets/fonts/League.ttf) format('truetype'),
}

@font-face {
  font-family: 'Segoe';
  src: local('Segoe'),
    url(../assets/fonts/SegoeUI/Segoe-UI.ttf) format('truetype'),
    url(../assets/fonts/SegoeUI/Segoe-UI-Bold.ttf) format('truetype'),
    url(../assets/fonts/SegoeUI/Segoe-UI-Bold-Italic.ttf) format('truetype'),
    url(../assets/fonts/SegoeUI/Segoe-UI-Italic.ttf) format('truetype'),
}

.h-fit-content {
  min-height: fit-content !important;
}
.w-fit-content {
  min-width: fit-content !important;
}

#q-app {
  min-height: 100vh;
}

.container {
  padding: 0px 20px;
}