import axios from 'axios';
import { useAuthStore } from 'src/stores/auth';
import * as AuthService from 'src/services/AuthService';
import { globalRouter } from 'src/router/globalRouter';
import { Loading } from 'quasar';
import { triggerNegative } from 'src/utils/triggers';

const auth = useAuthStore();

let retry = false;

const instance = axios.create({ baseURL: import.meta.env.VITE_BASE_URL, });

// Request interceptor for API calls
instance.interceptors.request.use(
  async (config) => {
    const { token, refreshToken } = auth.authData;
    if (token || refreshToken) {
      config.headers['Authorization'] = `Bearer ${refreshToken || token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
instance.interceptors.response.use(
  (response) => {
    const { refreshtoken } = response.headers;
    if (refreshtoken) {
      auth.storageAuthTokenSave({
        ...auth.authData,
        refreshToken: refreshtoken,
      });
    }
    return response;
  },
  async (error) => {
    console.log(error)
    if (import.meta.env.MODE == 'development') {
      console.error('🚀 ~ file: interceptor.ts:31 ~ error:', error);
    }
    const originalConfig = error.config;
    if (error.response) {
      if (error.response.status == 500 && (!retry || !originalConfig._retry)) {
        retry = true;
        originalConfig._retry = true;
        originalConfig.headers['Authorization'] = `Bearer ${auth.authData.refreshToken}`;

        return instance(originalConfig);
      } else if ( error.response.status == 401 || error.response.data?.message?.name == 'TokenExpiredError' || (error.response.status == 500 && retry) ) {
        if (import.meta.env.MODE == 'development') {
          console.info('🚀 ~ file: interceptor.ts:65 ~ : desconectando user', error.response.status);
        }
        
        if (error.response.status == 401){
          originalConfig.headers['Authorization'] = '';
          AuthService.signOut();
        }
        Loading.hide();
        globalRouter.router?.push('/ErrorUnauthorized');
        return Promise.resolve(error);
      } else if (error.response.status === 400 && error.response.data.message != 'Token não informado.'){
        return Promise.resolve(error);
      }
    }

    return Promise.reject(error);
  }
);

export { instance };
