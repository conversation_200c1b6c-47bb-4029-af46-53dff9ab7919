<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <!-- <div class="row w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold">ETAPA ATUAL DA SUA SOLICITAÇÃO</p>
      </div>
    <q-card-section class="w-full h-80 flex flex-col" v-if="flowLoaded && SolicitacaoSelecionada.status != 'CANCELADO'">
      <VueFlow :nodes="nodes" :edges="edges" :updatePos="updatePos" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'" :zoom-on-scroll="false" fit-view-on-init />
    </q-card-section> -->
    <div class="p-3 gap-3" v-if="User">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]" v-if="SolicitacaoSelecionada.id">
        <p class="col col-2 text-lg font-league font-bold">SOLICITAÇÃO</p>
        <div class="col col-2">
          <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA'" rounded color="green" label="Aguardando Analista" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'VERIFICANDO_SOLICITACAO'" class="p-2" rounded text-color="black" color="yellow" label="Verificando Solicitação" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO'" class="p-2" rounded color="orange" label="Aguardando Reunião" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO_FINAL'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'FINALIZADA'" class="p-2" rounded color="blue" label="Finalizada" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'EM_ANALISE'" class="p-2" rounded text-color="black" color="yellow" label="Em análise" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO_FINAL'" class="p-2" rounded color="red" label="Em atraso" />
          <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
        </div>
        <div class="col col-2" v-if="SolicitacaoSelecionada.pagamento.length > 0">
          <q-btn rounded color="primary" icon="payments" label="Pagamentos" @click="() => { router.push(`/solicitacao/pagamentos/${SolicitacaoSelecionada.id}`) }"/>
        </div>
      </div>
      <div v-if="SolicitacaoSelecionada.id" class="row w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold" >{{ SolicitacaoSelecionada.titulo }}</p>
      </div>
      <div class="row w-full p-[0px] sm:p-[15px]" v-else>
        <p class="col text-lg font-league font-bold">NOVA SOLICITAÇÃO</p>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]" v-if="SolicitacaoSelecionada.id">
        <q-card class="items-center w-full" v-if="SolicitacaoSelecionada">
            <q-card-section class="flex flex-col gap-2" v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO_FINAL' || SolicitacaoSelecionada.status === 'FINALIZADA'">
              <q-input
                v-if="SolicitacaoSelecionada.entrega_link"
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.entrega_link"
                label="Link da Análise"
                disable
              />
            </q-card-section>
        </q-card>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]" v-if="SolicitacaoSelecionada.id">
        <q-card class="items-center w-full" v-if="SolicitacaoSelecionada">
            <q-card-section class="flex flex-col gap-2">
              <q-input v-if="SolicitacaoSelecionada.colaborador"
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.colaborador.fullname"
                label="Analista responsável"
                disable
              />
              <q-input
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.titulo"
                label="Título"
                :disable="!isEditable"
              />
              <q-select
                emit-value
                map-options
                transition-show="jump-up"
                transition-hide="jump-up"
                v-model="SolicitacaoSelecionada.tipo"
                :options="optionsTipo"
                :disable="!isEditable"
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
                label="Tipo"
                outlined
                dense
              />
              <q-input
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.link_dados"
                label="Link dos Arquivos"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.variaveis"
                label="Variáveis"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.variaveis_dependentes"
                label="Variáveis Dependentes ou Desfecho"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.objetivo_geral"
                label="Objetivo Geral"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.objetivos_especificos"
                label="Objetivos Específicos"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.hipoteses"
                label="Hipóteses"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.detalhamentos"
                label="Detalhamentos"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.observacoes"
                label="Observações"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.instrumentos"
                label="Instrumentos"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.instrumentos_referencias"
                label="Referências dos Instrumentos"
                :disable="!isEditable"
              />
              <q-input
                :filled="!isEditable"
                type="textarea"
                :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
                v-model="SolicitacaoSelecionada.metodologia"
                label="Metodologia"
                :disable="!isEditable"
              />
            </q-card-section>
            <q-separator />
            <q-card-section>
              <q-card-actions>
                <q-btn
                  push
                  color="secondary"
                  text-color="white"
                  :label="'Voltar'"
                  @click="onClickVoltar()"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Salvar'"
                  v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA'"
                  @click="onClickAlterar"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Apropriar'"
                  v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA' && isColaborador"
                  @click="onClickApropriar"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Aguardando Reunião'"
                  v-if="SolicitacaoSelecionada.status === 'VERIFICANDO_SOLICITACAO' && isColaborador"
                  @click="onClickAguardandoReuniao"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Orçamento'"
                  v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO' && isColaborador"
                  @click="onClickOrcamento"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Orçamento'"
                  v-if="SolicitacaoSelecionada.status === 'EM_ANALISE' && isColaborador"
                  @click="onClickOrcamentoFinal"
                />
                <q-btn
                  push
                  color="primary"
                  text-color="white"
                  :label="'Cancelar'"
                  v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO' || SolicitacaoSelecionada.status === 'ATRASO' || SolicitacaoSelecionada.status === 'ATRASO_FINAL' && isColaborador"
                  @click="onClickCancelar"
                />
              </q-card-actions>
            </q-card-section>
        </q-card>
      </div>
      <div class="w-full p-[0px] sm:p-[15px]" v-else>
        <q-card class="items-center w-full" v>
          <q-card-section class="flex flex-col gap-2">
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.titulo"
              label="Título"
            />
            <q-select
              emit-value
              map-options
              transition-show="jump-up"
              transition-hide="jump-up"
              v-model="NovaSolicitacao.tipo"
              :options="optionsTipo"
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              label="Tipo"
              outlined
              dense
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.link_dados"
              label="Link dos Arquivos"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.variaveis"
              label="Variáveis"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.variaveis_dependentes"
              label="Variáveis Dependentes ou Desfecho"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.objetivo_geral"
              label="Objetivo Geral"
              type="textarea"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.objetivos_especificos"
              label="Objetivos Específicos"
              type="textarea"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.hipoteses"
              label="Hipóteses"
              type="textarea"
            />
            <q-input
            :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.detalhamentos"
              label="Detalhamentos"
              type="textarea"
            />
            <q-input
            :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.observacoes"
              label="Observações"
              type="textarea"
            />
            <q-input
            :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.instrumentos"
              label="Instrumentos"
              type="textarea"
            />
            <q-input
            :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.instrumentos_referencias"
              label="Referências dos Instrumentos"
              type="textarea"
            />
            <q-input
            :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="NovaSolicitacao.metodologia"
              label="Metodologia"
              type="textarea"
            />
            <q-checkbox right-label v-model="AcceptedTerms" label="Ao iniciar uma nova solicitação eu declaro que concordo com os Termos de Serviço da plataforma." />

          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-card-actions>
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Salvar'"
                @click="onClickCriarSolicitacao"
              />
            </q-card-actions>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <q-dialog v-model="dialogOrcamento" full-width>
      <q-card class="items-center w-full" v-if="Orcamento">
          <q-card-section>
            <div class="row">
              <div>
                <div class="text-h6" align="left">Solicitação {{SolicitacaoSelecionada.titulo}}</div>
              </div>
              <div class="col mt-2 ml-3">
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA'" rounded color="green" label="Aguardando Analista" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'VERIFICANDO_SOLICITACAO'" class="p-2" rounded text-color="black" color="yellow" label="Verificando Solicitação" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO'" class="p-2" rounded color="orange" label="Aguardando Reunião" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO_FINAL'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'FINALIZADA'" class="p-2" rounded color="blue" label="Finalizada" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'EM_ANALISE'" class="p-2" rounded text-color="black" color="yellow" label="Em análise" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO_FINAL'" class="p-2" rounded color="red" label="Em atraso" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
              </div>
            </div>
          </q-card-section>
          <q-card-section class="flex flex-col gap-2">
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="Orcamento.descricao"
              label="Descrição"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="Orcamento.vencimento"
              label="Vencimento (AAAA-MM-DD)"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model.number="Orcamento.valor"
              type="number"
              label="Valor"
            />
          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-card-actions>
              <q-btn
                push
                color="secondary"
                text-color="white"
                :label="'Voltar'"
                @click="dialogOrcamento = false"
              />
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Gerar Orçamento'"
                @click="onClickGerarOrcamento"
              />
            </q-card-actions>
          </q-card-section>
      </q-card>
    </q-dialog>
    <q-dialog v-model="dialogOrcamentoFinal" full-width>
      <q-card class="items-center w-full" v-if="OrcamentoFinal">
          <q-card-section>
            <div class="row">
              <div>
                <div class="text-h6" align="left">Solicitação {{SolicitacaoSelecionada.titulo}}</div>
              </div>
              <div class="col mt-2 ml-3">
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA'" rounded color="green" label="Aguardando Analista" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'VERIFICANDO_SOLICITACAO'" class="p-2" rounded text-color="black" color="yellow" label="Verificando Solicitação" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO'" class="p-2" rounded color="orange" label="Aguardando Reunião" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_PAGAMENTO_FINAL'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'FINALIZADA'" class="p-2" rounded color="blue" label="Finalizada" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'EM_ANALISE'" class="p-2" rounded text-color="black" color="yellow" label="Em análise" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO_FINAL'" class="p-2" rounded color="red" label="Em atraso" />
                <q-badge v-if="SolicitacaoSelecionada.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
              </div>
            </div>
          </q-card-section>
          <q-card-section class="flex flex-col gap-2">
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="OrcamentoFinal.descricao"
              label="Descrição"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="OrcamentoFinal.vencimento"
              label="Vencimento (AAAA-MM-DD)"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="OrcamentoFinal.valor"
              label="Valor"
            />
            <q-input
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              v-model="OrcamentoFinal.entrega_link"
              label="Link da análise"
            />
          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-card-actions>
              <q-btn
                push
                color="secondary"
                text-color="white"
                :label="'Voltar'"
                @click="dialogOrcamentoFinal = false"
              />
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Gerar Orçamento'"
                @click="onClickGerarOrcamentoFinal"
              />
            </q-card-actions>
          </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts">
import { UserType, useUserStore } from 'src/stores/user';
import { useRoute, useRouter } from 'vue-router'
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as SolicitacaoService from 'src/services/SolicitacaoService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';

// import { VueFlow } from '@vue-flow/core'
// import { initialEdges, initialNodes } from 'src/utils/initialElements';

export default defineComponent({
  name: 'SolicitacoesPage',
  // components: { VueFlow },
  setup () {
    const $q = useQuasar()
    const user = useUserStore();
    const route = useRoute()
    const router = useRouter()
    const User = ref<UserType>()
    const isColaborador = ref(false)
    const id = ref<string>()
    const isEditable = ref<boolean>()
    // const flowLoaded = ref<boolean>()
    const AcceptedTerms = ref<boolean>()
    const visibleColumns = ref<string[]>()
    const dialogOrcamento = ref<boolean>()
    const dialogOrcamentoFinal = ref<boolean>()
    const Orcamento = ref(
      {
        id: '',
        descricao: '',
        vencimento: '',
        valor: 0
      }
    )

    const OrcamentoFinal = ref(
      {
        id: '',
        descricao: '',
        vencimento: '',
        valor: 0,
        entrega_link: ''
      }
    )
    
    const Solicitacoes = ref([{ titulo: '' }])

    // const nodes = ref(initialNodes);
    // const edges = ref(initialEdges);

    // const updatePos = () => {
    //   nodes.value = nodes.value.map((node) => {
    //     return {
    //       ...node,
    //       position: {
    //         x: Math.random() * 400,
    //         y: Math.random() * 400,
    //       },
    //     };
    //   });
    // };

    const elements = ref([
    {
      id: '1',
      position: { x: 50, y: 50 },
      label: 'Node 1',
    }
  ]);
    watch(() => User.value, (newValue: UserType) => {
      if (!newValue) return
      isColaborador.value = newValue && (newValue.type == 'A' || newValue.type == 'C')
    })

    const optionsTipo = ref([
        {
          label: 'Projeto de Pesquisa',
          value: 'P',
        },
        {
          label: 'Dados já coletados',
          value: 'R',
        },
        {
          label: 'Outro',
          value: 'O',
        }
      ])
    

    const SolicitacaoSelecionada = ref(
      {
        colaborador: {
          fullname: ''
        },
        pagamento: [
          {
            url: '',
            name: ''
          }
        ],
        detalhamentos: '',
        entrega_link: '',
        hipoteses: '',
        id: '',
        instrumentos: '',
        instrumentos_referencias: '',
        link_dados: '',
        metodologia: '',
        objetivo_geral: '',
        objetivos_especificos: '',
        observacoes: '',
        status: '',
        tipo: '',
        titulo: '',
        usuario: {
          id: '',
          fullname: ''
        },
        variaveis: '',
        variaveis_dependentes: '',
        viabilidade_descricao: '',
        viabilidade_link: '',
        viabilidade_prazo: '',
        viabilidade_valor: '',
        xata: {
          createdAt: ''
        }
      }
    )

    const NovaSolicitacao = ref(
      {
        titulo: '',
        link_dados: '',
        objetivo_geral: '',
        objetivos_especificos: '',
        hipoteses: '',
        variaveis: '',
        variaveis_dependentes: '',
        detalhamentos: '',
        instrumentos: '',
        instrumentos_referencias: '',
        metodologia: '',
        observacoes: '',
        tipo: '',
      }
    )
    
    const columns = ref([
      { name: 'data', label: 'Data', align: 'left', field: 'data', sortable: true },
      { name: 'titulo', label: 'Título', align: 'left', field: 'titulo', sortable: true },
      { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
      { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
    ])
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    const getSolicitacao = async () => {
      // flowLoaded.value = false
      // for (let node of initialNodes){
      //   node.class = ''
      // }
      let nodeId = ''
      
      $q.loading.show({ message: 'Buscando solicitação' })
      const response = await SolicitacaoService.GetOne(id.value)
      if (axios.isAxiosError(response)){
        triggerNegative(response.response?.data.message)
        router.push('/solicitacoes')
        return
      }
      if (response.data){
        SolicitacaoSelecionada.value = response.data
        isEditable.value = false
        switch (SolicitacaoSelecionada.value.status) {
          case 'AGUARDANDO_ANALISTA':
            isEditable.value = true 
            nodeId = 'analise-1'
            break;
          case 'VERIFICANDO_SOLICITACAO':
            nodeId = 'analise-1'
            break;
          case 'EM_ANALISE':
            nodeId = 'analise-2'
            break;
          case 'AGUARDANDO_REUNIAO':
            nodeId = 'reuniao'
            break;
          case 'CANCELADO':
            nodeId = 'cancelado-atraso'
            break;
          case 'AGUARDANDO_PAGAMENTO':
            nodeId = 'orcamento-1'
            break;
            case 'ATRASO':
              nodeId = 'orcamento-1'
              break;
          case 'AGUARDANDO_PAGAMENTO_FINAL':
            nodeId = 'pagamento-2'
            break;
          case 'ATRASO_FINAL':
            nodeId = 'pagamento-2'
            break;
          case 'FINALIZADA':
            nodeId = 'finalizado'
            break;

          default:
            break;
        }
      } else {
        triggerNegative('Houve um erro ao buscar a solicitação')
      }
      // for (let node of initialNodes){
      //   node.class = ''
      //   if (node.id === nodeId){
      //     node.class = 'etapa_atual'
      //   }
      // }
      // flowLoaded.value = true
      $q.loading.hide()
    }

    onMounted( async () => {
      User.value = await user.storeUserDataGetter
      const idRoute = route.params.id
      if (typeof(idRoute) === 'string') {
        if (idRoute != 'create'){
          id.value = idRoute
          getSolicitacao()
        } else {
          // for (let node of initialNodes){
          //   if (node.id === 'cadastro'){
          //     node.class = 'etapa_atual'
          //   }
          // }
          // flowLoaded.value = true
        }
      }
      if ($q.screen.width <= 600){
        visibleColumns.value = ['titulo', 'status', 'data']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        ]
      } else {
        visibleColumns.value = ['titulo', 'status', 'data']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        ]
      }
    })

    const onClickCriarSolicitacao = async() => {
      $q.loading.show({ message: 'Criando Solicitação' })
      try {
        if (!NovaSolicitacao.value.titulo) {
          triggerNegative('Informe o título da solicitação')
          return
        }
        if (!NovaSolicitacao.value.tipo) {
          triggerNegative('Informe o tipo da solicitação')
          return
        }
        if (!AcceptedTerms.value) {
          triggerNegative('É necessário aceitar os termos de serviço da plataforma')
          return
        }
        const response = await SolicitacaoService.Criar(NovaSolicitacao.value)
        if (axios.isAxiosError(response)){
          triggerNegative(response.response.data.message)
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }
          router.push('/solicitacoes')
          return
        }
      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide()
      }
    }

    const onClickAlterar = async() => {
      $q.loading.show({ message: 'Alterando Solicitação' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Alterar(SolicitacaoSelecionada.value)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao alterar a solicitação')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide()
        getSolicitacao()
      }
    }

    const onClickApropriar = async() => {
      $q.loading.show({ message: 'Apropriando Solicitação' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Apropriar(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao apropriar a solicitação')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide()
        location.reload()
      }
    }
    const onClickCancelar = async() => {
      $q.loading.show({ message: 'Cancelando Solicitação' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Cancelar(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao cancelar a solicitação')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide()
        location.reload()
      }
    }

    const onClickAguardandoReuniao = async() => {
      $q.loading.show({ message: 'Reunião' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Reuniao(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao agendar reunião')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide()
        location.reload()
      }
    }
    
    const onClickOrcamento = async() => {
      Orcamento.value = {
        id: SolicitacaoSelecionada.value.id,
        descricao: '',
        vencimento: new Date().toISOString().split('T')[0],
        valor: 0
      }
      dialogOrcamento.value = true
    }

    const onClickOrcamentoFinal = async() => {
      OrcamentoFinal.value = {
        id: SolicitacaoSelecionada.value.id,
        descricao: '',
        vencimento: new Date().toISOString().split('T')[0],
        valor: 0,
        entrega_link: ''
      }
      dialogOrcamentoFinal.value = true
    }

    const onClickGerarOrcamento = async() => {
      $q.loading.show({ message: 'Gerando Orçamento' })
      try {
        if (!Orcamento.value) return
        const response = await SolicitacaoService.Orcar(Orcamento.value)
        if (axios.isAxiosError(response)){
          if (response.response){
            triggerNegative(response.response.data.message)
            dialogOrcamento.value = false
            $q.loading.hide()
          }
        } else {
          if (response.data){
            triggerSuccess(response.data)
            dialogOrcamento.value = false
            $q.loading.hide()
            // location.reload()
          }          
        }
      } catch (error) {
        console.log(error)
        dialogOrcamento.value = false
        $q.loading.hide()
      }
    }

    const onClickGerarOrcamentoFinal = async() => {
      $q.loading.show({ message: 'Gerando Orçamento' })
      try {
        if (!OrcamentoFinal.value) return
        const response = await SolicitacaoService.Entrega(OrcamentoFinal.value)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao gerar orçamento')
        } else {
          if (response.data){
            triggerSuccess(response.data)
            $q.loading.hide()
            location.reload()
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        dialogOrcamentoFinal.value = false
      }
    }

    const onClickVoltar = () => {
      if (isColaborador.value){
        router.push('/solicitacoes_colaborador')
      } else {
        router.push('/solicitacoes')
      }
    }

    return { 
      User,
      formatDate,
      formatDateApenasData,
      onClickCriarSolicitacao,
      onClickAlterar,
      isEditable,
      visibleColumns,
      Solicitacoes,
      SolicitacaoSelecionada,
      NovaSolicitacao,
      columns,
      optionsTipo,
      AcceptedTerms,
      router,
      isColaborador,
      onClickAguardandoReuniao,
      onClickOrcamento,
      onClickGerarOrcamento,
      onClickOrcamentoFinal,
      onClickGerarOrcamentoFinal,
      onClickApropriar,
      onClickCancelar,
      onClickVoltar,
      dialogOrcamento,
      dialogOrcamentoFinal,
      Orcamento,
      OrcamentoFinal,
      // elements,
      // edges,
      // nodes,
      // updatePos,
      // flowLoaded
     };


  }
});
</script>

<style>
.etapa_atual {
    background: rgb(153 0 204);
    color: white;
    border: 1px solid rgb(153 0 204);
    border-radius: 4px;
    box-shadow: 0 0 0 1px rgb(153 0 204);
    padding: 8px;
}
</style>