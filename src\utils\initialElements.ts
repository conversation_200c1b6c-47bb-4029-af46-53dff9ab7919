import { MarkerType, Position } from '@vue-flow/core';

export const initialNodes = [
  {
    id: 'cadastro',
    label: 'Cadastro da Solicitação',
    position: { x: 25, y: 25 },
    sourcePosition: Position.Right,
    type: 'input',
    class: ''
  },
  {
    id: 'analise-1',
    label: 'Análise do colaborador',
    position: { x: 200, y: 25 },
    sourcePosition: Position.Right,
    targetPosition: Position.Left,
  },
  {
    id: 'reuniao',
    label: 'Reuni<PERSON> de alinhamento',
    position: { x: 375, y: 25 },
    sourcePosition: Position.Bottom,
    targetPosition: Position.Left,
  },
  {
    id: 'orcamento-1',
    label: 'Orçamento',
    position: { x: 375, y: 100 },
    sourcePosition: Position.Left,
    targetPosition: Position.Top,
  },
  {
    id: 'analise-2',
    label: 'Análise dos Dados',
    position: { x: 200, y: 100 },
    sourcePosition: Position.Left,
    targetPosition: Position.Right,
  },
  {
    id: 'entrega',
    label: 'Entrega',
    position: { x: 25, y: 100 },
    sourcePosition: Position.Bottom,
    targetPosition: Position.Right,
  },
  {
    id: 'pagamento-2',
    label: 'Pagamento',
    position: { x: 115, y: 175 },
    sourcePosition: Position.Right,
    targetPosition: Position.Left,
  },
  {
    id: 'finalizado',
    label: 'Finalizado',
    position: { x: 300, y: 175 },
    type: 'output',
    targetPosition: Position.Left,
  },
];

export const initialEdges = [
  {
    id: 'cad-analise',
    source: 'cadastro',
    target: 'analise-1',
    markerEnd: MarkerType.ArrowClosed,
    animated: true
  },
  {
    id: 'analise-reuniao',
    source: 'analise-1',
    target: 'reuniao',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
  {
    id: 'reuniao-orcamento',
    source: 'reuniao',
    target: 'orcamento-1',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
  {
    id: 'orcamento-analise',
    source: 'orcamento-1',
    target: 'analise-2',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
  {
    id: 'analise-entrega',
    source: 'analise-2',
    target: 'entrega',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
  {
    id: 'entrega-pagamento',
    source: 'entrega',
    target: 'pagamento-2',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
  {
    id: 'pagamento-finalizado',
    source: 'pagamento-2',
    target: 'finalizado',
    markerEnd: MarkerType.ArrowClosed,
    animated: true,
  },
];
