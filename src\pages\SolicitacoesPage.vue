<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3" v-if="User">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold">SOLICITAÇÕES</p>
        <div class="text-center">
          <q-btn rounded color="primary" icon="add" label="Nova Solicitação" @click="router.push('/nova-solicitacao')"/>
        </div>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]">
        <q-table
          :rows="Solicitacoes"
          :columns="columns"
          :visible-columns="visibleColumns"
          row-key="name"
          :pagination="{rowsPerPage: 50}"
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="data" v-if="props.row.xata">
                {{ formatDateApenasData(props.row.xata.createdAt) }}
              </q-td>
              <q-td v-else></q-td>
              <q-td key="titulo" class="text-left" v-if="!visibleColumns?.indexOf('titulo')">
                {{ props.row.titulo }}
              </q-td>
              <q-td key="valor_total" class="text-center">
                <q-badge v-if="props.row.status === 'AGUARDANDO_ANALISTA'" class="p-2" rounded color="green" label="Aguardando Analista" />
                <q-badge v-if="props.row.status === 'VERIFICANDO_SOLICITACAO'" class="p-2" rounded color="yellow" text-color="black" label="Verificando Solicitação" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_REUNIAO'" class="p-2" rounded color="orange" label="Aguardando Reunião" />
                <q-badge v-if="props.row.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_PAGAMENTO'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_PAGAMENTO_FINAL'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="props.row.status === 'FINALIZADA'" class="p-2" rounded color="blue" label="Finalizada" />
                <q-badge v-if="props.row.status === 'EM_ANALISE'" class="p-2" rounded color="yellow" text-color="black" label="Em análise" />
                <q-badge v-if="props.row.status === 'ATRASO_FINAL'" class="p-2" rounded color="red" label="Em atraso" />
                <q-badge v-if="props.row.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
              </q-td>
              <q-td key="actions" class="text-center">
                <q-btn rounded color="primary" icon="search" @click="onClickDetalhes(props.row)">
                </q-btn>
              </q-td>
              <!--<q-td key="status" class="text-center">
                <q-btn disable v-if="props.row.status === 'AP'" class="bg-green-700 text-white w-full">
                    Aguardando Pagamento
                </q-btn>
                <q-btn disable v-if="props.row.status === 'P'" class="bg-green-500 text-white w-full">
                    Pago
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'F'" class="bg-blue-500 text-white w-full">
                    Finalizado
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'A'" class="bg-yellow-500 text-white w-full">
                    Aberto
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'C'" class="bg-red-700 text-white w-full">
                    Cancelado
                </q-btn>
              </q-td> -->
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="full-width row flex-center q-gutter-sm">
              <q-icon size="2em" name="sentiment_dissatisfied" />
              <span>
                Você ainda não tem solicitações.
              </span>
            </div>
          </template>
        </q-table>
      </div>
    </div>
  </q-page>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as SolicitacaoService from 'src/services/SolicitacaoService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';

export default defineComponent({
  name: 'SolicitacoesPage',
  components: {  },
  setup () {
    const $q = useQuasar()
    const user = useUserStore();
    const User = ref<UserType>()
    const router = useRouter()
    const visibleColumns = ref<string[]>()
    const Solicitacoes = ref([{ titulo: '' }])

    const SolicitacaoSelecionada = ref(
      {
        colaborador: {
          fullname: ''
        },
        pagamento: [
          {
            url: '',
            name: ''
          }
        ],
        detalhamentos: '',
        entrega_link: '',
        hipoteses: '',
        id: '',
        instrumentos: '',
        instrumentos_referencias: '',
        link_dados: '',
        metodologia: '',
        objetivo_geral: '',
        objetivos_especificos: '',
        observacoes: '',
        status: '',
        tipo: '',
        titulo: '',
        usuario: {
          id: '',
          fullname: ''
        },
        variaveis: '',
        viabilidade_descricao: '',
        viabilidade_link: '',
        viabilidade_prazo: '',
        viabilidade_valor: '',
        xata: {
          createdAt: ''
        }
      }
    )

    const NovaSolicitacao = ref(
      {
        titulo: '',
        link_dados: '',
        objetivo_geral: '',
        objetivos_especificos: '',
        hipoteses: '',
        variaveis: '',
        detalhamentos: '',
        instrumentos: '',
        instrumentos_referencias: '',
        metodologia: '',
        observacoes: '',
        tipo: '',
      }
    )
    
    const columns = ref([
      { name: 'data', label: 'Data', align: 'left', field: 'data', sortable: true },
      { name: 'titulo', label: 'Título', align: 'left', field: 'titulo', sortable: true },
      { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
      { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
    ])
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    const getSolicitacoes = async () => {
      $q.loading.show({ message: 'Buscando solicitações' })
      const response = await SolicitacaoService.Get()
      if (response.data){
        Solicitacoes.value = response.data
        await formatSolicitacoes()
      } else {
        triggerNegative('Houve um erro ao buscar as solicitações')
      }
      $q.loading.hide()
    }

    const formatSolicitacoes = async () => {
      for (let Solicitacao of Solicitacoes.value){
        if (Solicitacao.titulo.length >= 75){
          Solicitacao.titulo = `${Solicitacao.titulo.substring(0, 75)}...`
        }
      }
    }

    onMounted( async () => {
      User.value = await user.storeUserDataGetter
      getSolicitacoes()
      if ($q.screen.width <= 600){
        visibleColumns.value = ['titulo', 'status', 'data']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
          // { name: 'total', align: 'right', label: 'Total', field: 'total', sortable: true },
          // { name: 'status', align: 'center', label: 'Status', field: 'status', sortable: true },
        ]
      } else {
        visibleColumns.value = ['titulo', 'status', 'data']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
          // { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDate(row.xata.createdAt) }, sortable: true },
          // { name: 'total', align: 'right', label: 'Total', field: 'total', sortable: true },
          // { name: 'status', align: 'center', label: 'Status', field: 'status', sortable: true },
        ]
      }
    })

    const onClickDetalhes = async (Solicitacao: any) => {
      router.push(`/solicitacao/${Solicitacao.id}`)
    }

    return { 
      User,
      formatDate,
      formatDateApenasData,
      onClickDetalhes,
      visibleColumns,
      Solicitacoes,
      SolicitacaoSelecionada,
      NovaSolicitacao,
      columns,
      router
     };


  }
});
</script>

<style scoped>

</style>