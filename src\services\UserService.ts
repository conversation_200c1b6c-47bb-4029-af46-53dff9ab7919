import { instance } from 'src/services/Interceptor/interceptor';
import { environment } from '../environment/environment';
import { UpdateUser } from 'src/types/interfaces/IUser';
import { StorageAuthTokenProps, useAuthStore } from 'src/stores/auth';
import { UserType, useUserStore } from 'src/stores/user';

function updateAuthStore(authentication: StorageAuthTokenProps){
  const auth = useAuthStore()
  auth.storageAuthTokenSave(authentication)
}

function updateUserStore(userProps: UserType){
  const user = useUserStore()
  user.storageUserSave(userProps)
}

export function GetUser(id: string) {
  return instance.get(`${environment.baseUrl}/user/${id}`);
}

export function Get() {
  return instance.get(`${environment.baseUrl}/user`);
}

export async function Update(id: string, data: UpdateUser) {
  const response = await instance.patch(`${environment.baseUrl}/user/${id}`, data);
  if (response.data){
    if (response.data.success){
      updateAuthStore({ 
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        gender: response.data.usuario.gender,
        token: null,
        refreshToken: ''
      });
    
      updateUserStore({
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        type: response.data.usuario.type
      })
    }
  }
  return response
}