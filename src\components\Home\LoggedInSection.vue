<template>
  <q-page :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <section class="w-full lg:h-auto p-4" >
    <div class="flex flex-col items-start justify-center gap-5 w-full">
      <span class="text-lg font-bold" :class="$q.dark.isActive ? 'text-white' : 'text-[#292321]'">
        Bem-vindo, <span :class="$q.dark.isActive ? 'text-accent' : 'text-primary'">{{ User?.fullname.split(' ')[0] }}</span>
      </span>
      <q-card class="rounded-lg w-full">
        <q-card-section
          class="flex lg:flex-nowrap items-center justify-center gap-3 p-5 w-full"
        >
          <q-card
            class="border-l-[5px] border-[var(--q-primary)] rounded-lg w-full"
            :class="`
                  ${
                      `${
                          $q.dark.isActive
                            ? 'bg-[var(--q-dark-page)] hover:bg-slate-700'
                            : 'bg-[var(--color-light-gray)] hover:bg-gray-200'
                        }`
                  }`"
            v-for="(item) in [
              {
                name: 'Solicitações',
                label: 'Suas solicitações de análise',
                icon: 'bolt',
                to: `/solicitacoes`,
              },
              {
                name: 'Perfil',
                label: 'Edite seu perfil',
                icon: 'person',
                to: `/perfil/${User?.id}`,
              },
              {
                name: 'Biblioteca',
                label: 'Confira os livros indicados pela Habilis',
                icon: 'library_books',
                to: `/biblioteca`,
              },
            ]"
            :key="item"
          >
            <q-card-section
              class="flex flex-col gap-3 items-start justify-center h-full cursor-pointer"
              @click="
                  this.$router.push(item.to);
              "
            >
              <q-tooltip
                anchor="top middle"
                self="bottom middle"
                class="bg-primary"
              >
                {{ item.label }}
              </q-tooltip>
              <div class="flex flex-nowrap gap-3 items-center justify-between">
                <div
                  class="flex items-center justify-center rounded-full bg-primary w-[45px] h-[45px]"
                >
                  <q-icon :name="item.icon" color="white" size="25px" />
                </div>
                <span
                  class="font-bold text-lg"
                  v-if="true"
                >
                  {{ item.name }}
                </span>
                <q-skeleton type="rect" width="65px" v-else />
              </div>
            </q-card-section>
          </q-card>
        </q-card-section>
      </q-card>
      <q-card class="rounded-lg w-full">
        <q-card-section>
          <div class="text-lg font-medium" :class="$q.dark.isActive ? 'text-white' : 'text-[#292321]'">Conheça um pouco mais sobre a Habilis.</div>
        </q-card-section>
        <q-card-section
          class="flex lg:flex-nowrap items-center justify-center gap-3 p-5 w-full"
        >
          <q-card
            class="border-l-[5px] border-[var(--q-primary)] rounded-lg w-full"
            :class="`
                  ${
                      `${
                          $q.dark.isActive
                            ? 'bg-[var(--q-dark-page)] hover:bg-slate-700'
                            : 'bg-[var(--color-light-gray)] hover:bg-gray-200'
                        }`
                  }`"
            v-for="(item) in [
              {
                name: 'Portfólio',
                label: 'Visualize um exemplo do nosso trabalho',
                icon: 'import_contacts',
                to: `/portfolio`,
              },
              {
                name: 'WhatsApp',
                label: 'Manda um zap!',
                icon: 'bi-whatsapp',
                to: `/contato/whatsapp`,
              },
              {
                name: 'Instagram',
                label: 'Segue a gente no insta',
                icon: 'bi-instagram',
                to: `/contato/instagram`,
              }
            ]"
            :key="item"
          >
            <q-card-section
              class="flex flex-col gap-3 items-start justify-center h-full cursor-pointer"
              @click="
                  this.$router.push(item.to);
              "
            >
              <q-tooltip
                anchor="top middle"
                self="bottom middle"
                class="bg-primary"
              >
                {{ item.label }}
              </q-tooltip>
              <div class="flex flex-nowrap gap-3 items-center justify-between">
                <div
                  class="flex items-center justify-center rounded-full bg-primary w-[45px] h-[45px]"
                >
                  <q-icon :name="item.icon" color="white" size="25px" />
                </div>
                <span
                  class="font-bold text-lg"
                  v-if="true"
                >
                  {{ item.name }}
                </span>
                <q-skeleton type="rect" width="65px" v-else />
              </div>
            </q-card-section>
          </q-card>
        </q-card-section>
      </q-card>
    </div>
  </section>
  </q-page>
</template>
<script>
import { useUserStore } from 'src/stores/user';
import { inject, ref, watch, onMounted } from 'vue';
import { getImageUrl } from 'src/utils/image';
import { useRouter } from 'vue-router';

export default {
  name: 'LoggedInSection',
  // components: { AnatomyButton },
  setup() {
    const user = useUserStore();
    const User = ref(null);
    const bus = inject('bus');
    const router = useRouter()

    const handleLogin = () => {
      bus.emit('handleLogin');
    };

    const handlePortfolio = () => {
      router.push('/portfolio')
    };

    watch(() => user.userData, (newValue) => {
      User.value = newValue
    })

    const getUser = async () => {
      User.value = await user.storeUserDataGetter;
    };

    onMounted(() => {
      getUser();
    })

    return { 
      User,
      slide: ref('card_1'), 
      getImageUrl,
      handleLogin,
      handlePortfolio,
     };
  },
};
</script>
<style lang="scss">
.welcome {
  line-height: 150%;
  letter-spacing: 0.8px;
}
.main-text {
  // color: #000;
  font-weight: 600;
  line-height: 135%;
}
.desc-text {
  // color: var(--neutral-colors-300, #525b67);
  font-style: normal;
  font-weight: 400;
  line-height: 160%; /* 28.8px */
}

/* Classes personalizadas */
.group {
  position: relative;
  display: inline-block;
}

.bg-effect {
  transform: translateX(-100%) scale(1);
  border-radius: 50%;
}

.group:hover .bg-effect {
  transform: translateX(0) scale(1.5);
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.rounded-md {
  border-radius: 0.375rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.transition-all {
  transition: all 0.5s;
}

.duration-500 {
  transition-duration: 0.5s;
}

.z-10 {
  z-index: 10;
}
</style>