import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/IndexPage.vue') },
      { path: '/auth/google/:token', component: () => import('pages/GooglePage.vue'), props: true },
      { path: 'perfil/:id', component: () => import('pages/PerfilPage.vue') },
      { path: '/solicitacoes', component: () => import('pages/SolicitacoesPage.vue') },
      { path: '/solicitacoes_colaborador', component: () => import('pages/SolicitacoesColaboradorPage.vue') },
      { path: '/usuarios', component: () => import('pages/UsuariosPage.vue') },
      { path: '/dashboard', component: () => import('pages/DashboardPage.vue') },
      { path: '/biblioteca', name: 'biblioteca', component: () => import('pages/BibliotecaPage.vue') },
      { path: '/solicitacao/:id', component: () => import('pages/SolicitacaoPage.vue'), props: true },
      { path: '/nova-solicitacao', component: () => import('pages/NovaSolicitacaoPage.vue'), props: true },
      { path: '/solicitacao/pagamentos/:id', component: () => import('pages/PagamentosPage.vue'), props: true },
      { path: '/portfolio', component: () => import('pages/PortfolioPage.vue'), props: true },
      { path: '/novo-usuario', component: () => import('pages/NovoUsuarioPage.vue'), props: true },
      { path: '/contato/:type', component: () => import('pages/contato/WhatsappPage.vue'), props: true },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
