<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3" v-if="User">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold">USUÁRIOS</p>
        <div class="text-center">
          <q-btn rounded color="primary" icon="add" label="Cadastrar novo usuário" @click="router.push('/novo-usuario')"/>
        </div>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]">
        <q-table
          :rows="Usuarios"
          :columns="columns"
          :visible-columns="visibleColumns"
          row-key="name"
          :pagination="{rowsPerPage: 50}"
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="fullname">
                {{ props.row.fullname }}
              </q-td>
              <q-td key="email">
                {{ props.row.email }}
              </q-td>
              <q-td key="type" class="text-left" >
                <p v-if="props.row.type === 'A'">Administrador</p>
                <p v-if="props.row.type === 'C'">Colaborador</p>
                <p v-if="props.row.type === 'U'">Usuário</p>
              </q-td>
              <q-td key="last_online" class="text-left">
                {{ props.row.last_online_date }}
              </q-td>
              <q-td key="actions" class="text-center">
                <q-btn rounded color="blue-6" icon="edit" @click="onClickEdit(props.row.id)"/>
                <q-btn rounded color="green-6" icon="chat" class="ml-2" @click="onClickChat(props.row)">
                  <q-badge color="red" floating v-if="props.row.count_not_seen > 0">{{props.row.count_not_seen}}</q-badge>
                </q-btn>
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="full-width row flex-center q-gutter-sm">
              <q-icon size="2em" name="sentiment_dissatisfied" />
              <span>
                Sem usuários
              </span>
            </div>
          </template>
        </q-table>
      </div>
      <q-dialog v-model="dialogChat" transition-show="jump-down" transition-hide="jump-up">
        <q-card style="width: 700px; max-width: 80vw;">
          <q-card-section>
            <div class="text-h6">Fale conosco!</div>
          </q-card-section>
          <q-card-section v-if="chatMessages.length > 0">
            <q-scroll-area style="height: 400px; max-width: 650px;">
            <div>
              <q-chat-message v-for="(message, index) in chatMessages" :key="index"
              :name="message.collab ? message.collab.fullname : message.user.fullname"
              :text="[message.message]"
              :sent="message.collab ? true : false"
              :stamp="message.xata.stamp"
              :bg-color="message.collab ? 'green-4' : 'grey-5'"
              >
              </q-chat-message>
            </div>
            </q-scroll-area>
          </q-card-section>
          <q-card-section>
            <div class="row gap-2">
              <q-input class="col col-10 grow" v-model="chatMessageInput" :label="'Escreva sua mensagem'" outlined dense >
                <template v-slot:prepend>
                  <q-icon name="text" size="15px" />
                </template>
              </q-input>
              <q-btn
                push
                class="col col-1"
                color="primary"
                text-color="white"
                icon="send"
                no-caps
                @click="onClickSendMessage"
              />
            </div>
          </q-card-section>
        </q-card>
      </q-dialog>
    </div>
  </q-page>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';
import * as ChatService from 'src/services/ChatService'
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData, formatDateChat } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';
import { io, Socket } from 'socket.io-client';
import { environment } from 'src/environment/environment';
import { useAuthStore } from 'src/stores/auth';

export default defineComponent({
  name: 'UsuariosPage',
  components: {  },
  setup () {
    const $q = useQuasar()
    const user = useUserStore();
    const auth = useAuthStore();
    const User = ref<UserType>()
    const router = useRouter()
    const visibleColumns = ref<string[]>()
    const Usuarios = ref([{ 
      id: '',
      fullname: '',
      email: '',
      type: '',
      last_online: '',
      last_online_date: '',
      count_not_seen: 0
     }])

    const SocketIo = ref<Socket>()
    let dialogChat = ref(false)
    const chatMessageInput = ref('')
    const chatMessages = ref([
      {
        collab: {
          fullname: '',
          id: ''
        },
        user: {
          fullname: '',
          id: ''
        },
        message: '',
        xata: {
          createdAt: '',
          stamp: ''
        }
      }
    ])

    const UsuarioSelecionado = ref(
      {
        fullname: '',
        id: ''
      }
    )
    
    const columns = ref([
      { name: 'fullname', label: 'Nome', align: 'left', field: 'fullname', sortable: true },
      { name: 'email', label: 'E-mail', align: 'left', field: 'email', sortable: true },
      { name: 'type', label: 'Tipo', align: 'left', field: 'type', sortable: true },
      { name: 'last_online_date', label: 'Última vez online', align: 'left', field: 'last_online_date', sortable: true },
      { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
    ])
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    watch(() => dialogChat.value, (newValue: boolean) => {
      if (newValue === false){
        getMessagesNotSeen()
      }
    })

    const getMessagesNotSeen = async () => {
      for (let Usuario of Usuarios.value){
        const responseChat = await ChatService.GetMessagesNotSeen(Usuario.id)
        Usuario.count_not_seen = responseChat.data
      }
    }

    const getUsuarios = async () => {
      $q.loading.show({ message: 'Buscando usuários' })
      const response = await UserService.Get()
      if (response.data){
        Usuarios.value = response.data

        getMessagesNotSeen()
        setInterval(() => {
          getMessagesNotSeen()
        }, 10000)

        for (let Usuario of Usuarios.value){
          Usuario.last_online_date = await formatDateChat(Usuario.last_online)
        }

      } else {
        triggerNegative('Houve um erro ao buscar os usuários')
      }
      $q.loading.hide()
    }

    onMounted( async () => {
      User.value = await user.storeUserDataGetter
      const isColaborador = User.value.type == 'C' || User.value.type == 'A'

      if (!isColaborador) {
        router.push('/');
        return
      }
      getUsuarios()
      if ($q.screen.width <= 600){
        // visibleColumns.value = ['titulo', 'status', 'data']
        // columns.value = [
        //   { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
        //   { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
        //   { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
        //   { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        // ]
      } else {
        // visibleColumns.value = ['titulo', 'status', 'data']
        // columns.value = [
        //   { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
        //   { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
        //   { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
        //   { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        // ]
      }
    })

    const onClickChat = async (Usuario: any) => {
      UsuarioSelecionado.value = Usuario
      const responseChat = await ChatService.GetMessages(Usuario.id)
      chatMessages.value = []
      for (let message of responseChat.data){
        message.xata.stamp = await formatDateChat(message.xata.createdAt)
        chatMessages.value.push(message)
      }

      SocketIo.value = io(environment.baseUrl, {
        extraHeaders: {
          Authorization: `Bearer ${auth.storeAuthTokenGetter}`
        }
      });

      SocketIo.value.on('chat', async (argument: any) => {
        if (UsuarioSelecionado.value.id === argument.usuario.id){
          chatMessages.value.push({
            user: {
              fullname: UsuarioSelecionado.value.fullname,
              id: UsuarioSelecionado.value.id
            },
            collab: null,
            message: argument.message,
            xata: {
              createdAt: `${new Date()}`,
              stamp: `${await formatDateChat('')}`
            }
          })
        }
      })


      dialogChat.value = true
    }

    const onClickEdit = async (id: string) => {
      router.push(`/perfil/${id}`)
    }

    const onClickSendMessage = async () => {
      if (!User.value) return
      SocketIo.value?.emit('chat', {
        usuario: {
          id: UsuarioSelecionado.value.id
        },
        collab: {
          id: User.value.id
        },
        message: chatMessageInput.value
      })

      chatMessages.value.push({
        user: {
          fullname: UsuarioSelecionado.value.fullname,
          id: UsuarioSelecionado.value.id
        },
        collab: {
          fullname: `${User.value?.fullname}`,
          id: `${User.value?.id}`
        },
        message: chatMessageInput.value,
        xata: {
          createdAt: `${new Date()}`,
          stamp: `${await formatDateChat('')}`
        }
      })

      chatMessageInput.value = ''
      // try {
      //   const response = await AuthService.Login({...formData.value})
      //   if (axios.isAxiosError(response)) {
      //     if (response.response){
      //       triggerNegative(response.response.data.message);
      //     } else {
      //       triggerNegative('Houve um erro ao fazer o Login');
      //     }
      //     return
      //   }
      //   isLogged.value = true
      //   dialogLogin.value = false

      //   formData.value = { email: '', password: '' }
      // } catch (error) {
      //   console.log(error)
      //   throw error;
      // } finally {
      //   $q.loading.hide();
      // }
    };

    return { 
      User,
      formatDate,
      formatDateChat,
      formatDateApenasData,
      onClickChat,
      onClickEdit,
      visibleColumns,
      Usuarios,
      dialogChat,
      chatMessages,
      chatMessageInput,
      columns,
      router,
      onClickSendMessage
     };


  }
});
</script>

<style scoped>

</style>