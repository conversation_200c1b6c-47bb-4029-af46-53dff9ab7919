import { defineStore } from 'pinia';
import { AUTH_STORAGE } from 'src/stores/storageConfig';

export type StorageAuthTokenProps = {
  id: string,
  email: string,
  cgc: string,
  fullname: string,
  cellphone_number: string,
  token: string | null,
  refreshToken: string,
  gender: string,
};

const defaultData = {
    id: '',
    email: '',
    cgc: '',
    fullname: '',
    cellphone_number: '',
    token: '',
    refreshToken: '',
    gender: null,
};

const getAuthStorage = () => {
  const response = localStorage.getItem(AUTH_STORAGE);
  const data: StorageAuthTokenProps = response
    ? JSON.parse(response)
    : defaultData;
  return data;
};

export const useAuthStore = defineStore(AUTH_STORAGE, {
  state: () => ({
    authData: getAuthStorage(),
  }),
  getters: {
    storeAuthTokenGetter(state) {
      return state.authData.token;
    },
    storeAuthRefreshGetter(state) {
      return state.authData.refreshToken;
    },
  },
  actions: {
    storageAuthTokenSave(newAuthData: StorageAuthTokenProps) {
      console.log('🤬 ~ storageAuthTokenSave ~ newAuthData:', newAuthData)
      if (!newAuthData.token) newAuthData.token = this.authData.token
      this.authData = newAuthData;
      localStorage.setItem(AUTH_STORAGE, JSON.stringify(this.authData));
    },
    storageAuthTokenRemove() {
      this.authData = defaultData;
      localStorage.removeItem(AUTH_STORAGE);
    },
  },
});
