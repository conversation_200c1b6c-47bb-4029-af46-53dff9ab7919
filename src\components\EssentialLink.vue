<template>
  <q-item clickable tag="a" :href="link" >
    <q-item-section v-if="icon" avatar >
      <q-icon :name="icon" />
    </q-item-section>

    <q-item-section>
      <q-item-label style="font-size: 18px; font-weight: 400;">{{ title }}</q-item-label>
    </q-item-section>
  </q-item>
  <q-separator />
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'EssentialLink',
  props: {
    title: {
      type: String,
      required: true
    },

    caption: {
      type: String,
      default: ''
    },

    link: {
      type: String,
      default: '#'
    },

    icon: {
      type: String,
      default: ''
    }
  }
});
</script>
