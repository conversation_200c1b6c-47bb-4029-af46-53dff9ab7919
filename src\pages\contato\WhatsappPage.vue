<template>
  <p></p>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router'

export default defineComponent({
  name: 'WhatsappPage',
  components: { },
  setup () {
    const route = useRoute()
    const router = useRouter()

    onMounted(() => {
      const type = route.params.type
      switch (type) {
        case 'whatsapp':
          window.open('https://wa.me/555591023468?text=Olá,%20gostaria%20de%20mais%20informações.', '_blank');
          break;
        case 'instagram':
          window.open('https://www.instagram.com/habilis.tech/', '_blank');
          break;
        default:
          break;
      }
      router.push('/')
    })
   return {
    };
  }
});
</script>