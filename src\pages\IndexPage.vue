<template>
  <q-page
    :class="$q.dark.isActive ? 'bg-inherit' : 'bg-white'"
  >
    <LoggedInSection v-if="userLogged" id="logged" />
    <MainSection v-else id="main" />
    <FooterSection id="main" />
    <div class="mx-5 gap-3 mt-2 p-1">
      <q-dialog
        v-model="dialogLogin"
        transition-show="jump-down"
        transition-hide="jump-up"
      >
        <q-card style="width: 70vw">
          <q-card-section>
            <div class="text-h6">
              Faça login no canto direito superior para iniciar!
            </div>
          </q-card-section>
        </q-card>
      </q-dialog>
    </div>
  </q-page>
</template>

<script lang="ts">
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import MainSection from '../components/Home/MainSection.vue';
import FooterSection from '../components/Home/FooterSection.vue';
import { Produtos } from 'src/classes/produto';
import LoggedInSection from 'src/components/Home/LoggedInSection.vue';

interface ICard {
  img: string;
  name: string;
  text: string;
  linkLinkedin: string;
  linkInstagram: string;
}
interface IButton {
  icon: string;
  isActive: boolean;
  // func: () => toogleDialog,
  text: string;
  progressValue: number;
}

export default defineComponent({
  name: 'IndexPage',
  components: { MainSection, LoggedInSection, FooterSection },
  setup() {
    const listCards: ICard[] = [
      {
        img: 'https://media.licdn.com/dms/image/D4D03AQEh5eYio6ynoQ/profile-displayphoto-shrink_800_800/0/1687560226814?e=2147483647&v=beta&t=G6Qt-4TwfeAfwLmEGblE0R47QRQw8zK1bIFeypIsgZo',
        name: 'Rodrigo Fioravanti',
        text: 'Analista de Dados, Licenciado em Matemática, Mestre e Doutor em Ensino de Ciências com foco em Estatística. Mais de 10 anos deexperiência em análise de dados acadêmicos.',
        linkLinkedin:
          'https://www.linkedin.com/in/fioravanti-statisticsteaching/',
        linkInstagram: 'https://www.instagram.com/fioravantirodrigop/',
      },
      {
        img: 'https://i.imgur.com/nTRIMna.jpg',
        name: 'Luan Vieira',
        text: 'Desenvolvedor de Software, Bacharel em Sistemas de Informação e apaixonado por inteligência artificial.',
        linkLinkedin: 'https://www.linkedin.com/in/luan-vieira-55245b1b5//',
        linkInstagram: 'https://www.instagram.com/luanvieira.er/',
      },
    ];

    const listProdutos1 = new Produtos('Categoria 1', []);
    listProdutos1.addProduct('Análise Exploratória', 'Investigação inicial de dados desconhecidos', 'map');
    listProdutos1.addProduct('Visualização de Dados', 'Representação gráfica de informações', 'visibility');
    listProdutos1.addProduct('Data Story', 'Narrativa guiada por dados', 'library_books');
    listProdutos1.addProduct('Testes de Hipóteses', 'Comparação estatística de suposições', 'search');

    const listProdutos2 = new Produtos('Categoria 2', []);
    listProdutos2.addProduct('Delineamento de Pesquisa Quantitativa', 'Estruturação de estudo numérico', 'square_foot');
    listProdutos2.addProduct('Correlação e Regressão', 'Relação e predição entre variáveis', 'query_stats');
    listProdutos2.addProduct('Intervalos de Confiança', 'Faixa provável de resultados', 'scale');
    listProdutos2.addProduct('Interpretação de Resultados', 'Decifrar achados estatísticos', 'lightbulb');

    const listProdutos3 = new Produtos('Categoria 3', []);
    listProdutos3.addProduct('Escrita Científica', 'Redação de descobertas acadêmicas', 'edit');
    listProdutos3.addProduct('Relatórios de Análise', 'Sumário detalhado de análises', 'folder');
    listProdutos3.addProduct('Metodologia de Pesquisa', 'Abordagem sistemática de estudo', 'database');
    listProdutos3.addProduct('Mentoria em Análise de Dados Acadêmicos', 'Orientação em análise estatística',
    'school');

    const router = useRouter();
    let dialogLogin = ref(false);
    const user = useUserStore();
    const User = ref<UserType>();

    const userLogged = ref(false)

    const listButtons: IButton[] = [
      {
        icon: 'person',
        isActive: false,
        text: '1 - Faça login na plataforma',
        progressValue: 0,
      },
      {
        icon: 'app_registration',
        isActive: false,
        text: '2 - Cadastre sua solicitação',
        progressValue: 0.25,
      },
      {
        icon: 'payments',
        isActive: false,
        text: '3 - Receba o orçamento',
        progressValue: 0.5,
      },
      {
        icon: 'search',
        isActive: false,
        text: '4 - Receba a análise de viabilidade',
        progressValue: 0.75,
      },
      {
        icon: 'favorite',
        isActive: false,
        text: '5 - Tenha sua análise final em mãos',
        progressValue: 1,
      },
    ];

    const progress = ref(0);

    watch(
      () => user.userData,
      (newValue: UserType) => {
        User.value = newValue;
        if (newValue?.id) {
          userLogged.value = true
        } else {
          userLogged.value = false
        }
      }
    );

    watch(
      () => User.value,
      (newValue) => {
        if (newValue?.id) {
          userLogged.value = true
        } else {
          userLogged.value = false
        }
      }
    );

    const getUser = async () => {
      User.value = await user.storeUserDataGetter;
    };

    const toggleDialogLogin = async () => {
      if (User.value?.id) {
        router.push('/solicitacao/create');
      } else {
        dialogLogin.value = true;
      }
    };

    onMounted(() => {
      getUser();
      let currentStep = 0;

      listButtons[currentStep].isActive = true;
      progress.value = listButtons[currentStep].progressValue;
      setInterval(() => {
        for (let i = 0; i < listButtons.length; i++) {
          if (i === currentStep) {
            listButtons[i].isActive = true;
            progress.value = listButtons[i].progressValue;
          } else {
            listButtons[i].isActive = false;
          }
        }

        currentStep++;
        if (currentStep >= listButtons.length) {
          currentStep = 0;
        }
      }, 3000);
    });

    return {
      listCards,
      listButtons,
      slide1: ref('Análise Exploratória'),
      slide2: ref('Delineamento de Pesquisa Quantitativa'),
      slide3: ref('Escrita Científica'),
      progress,
      dialogLogin,
      router,
      toggleDialogLogin,
      userLogged,
    };
  },
});
</script>
<style lang="scss" scoped></style>
