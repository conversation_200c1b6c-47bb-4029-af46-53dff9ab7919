{"name": "habilis-app", "version": "1.0.0,", "description": "<PERSON> da Habilis", "productName": "<PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@capacitor/geolocation": "^5.0.7", "@fawmi/vue-google-maps": "0.9.67", "@quasar/extras": "^1.16.4", "@vue-flow/core": "^1.33.5", "@vueuse/motion": "^2.2.3", "axios": "^1.6.7", "javascript-time-ago": "^2.5.9", "pinia": "^2.1.7", "qrcode": "^1.5.3", "qrious": "^4.0.2", "quasar": "^2.6.0", "socket.io-client": "^4.7.5", "vue": "^3.0.0", "vue-i18n": "^10.0.0-beta.1", "vue-router": "^4.0.0"}, "devDependencies": {"@quasar/app-vite": "^1.3.0", "@types/node": "^12.20.21", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.17", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.35", "prettier": "^2.5.1", "tailwindcss": "^3.4.1", "typescript": "^4.5.4"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}