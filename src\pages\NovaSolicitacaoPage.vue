<template>
  <q-page
    class="w-full h-full"
    :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'"
  >
    <div
      class="container flex flex-col items-center justify-start gap-3 py-5 h-full"
    >
      <div class="flex items-center justify-between w-full q-pa-md">
        <span class="text-4xl font-league font-bold"> Nova Solicitação </span>
        <q-select
          emit-value
          map-options
          transition-show="jump-up"
          transition-hide="jump-up"
          v-model="NovaSolicitacao.user_id"
          :options="optionsUsers"
          :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2'"
          label="Usuário"
          :disable="User?.type == 'U'"
        />
      </div>
      <q-stepper
        header-nav
        class="w-full"
        v-model="step"
        ref="stepper"
        :active-color="`${$q.dark.isActive ? 'white' : 'black'}`"
        done-color="green"
        :vertical="$q.screen.lt.md"
        :contracted="$q.screen.lt.md"
        flat
        animated
      >
        <q-step
          :name="1"
          title="Tipo da Solicitação"
          icon="settings"
          :done="step > 1"
        >
          <div
            class="grid grid-cols-1 lg:grid-cols-3 items-stretch justify-center gap-4 w-full"
          >
            <q-card
              :class="`${
                $q.dark.isActive
                  ? 'bg-[var(--q-dark-page)] hover:bg-slate-700 hover:cursor-pointer'
                  : 'bg-[var(--color-light-gray)] hover:bg-gray-200 hover:cursor-pointer'
              }`"
              class="w-full rounded-xl min-h-52 flex flex-col justify-between"
              @click="secondStep('P')"
            >
              <q-card-section
                class="flex flex-col justify-center gap-3 w-full flex-grow"
                bordered
              >
                <div class="flex flex-col w-full my-2">
                  <span class="font-bold text-lg text-primary">
                    Projeto de Pesquisa
                  </span>
                  <span
                    class="font-semibold text-md"
                    :class="`${$q.dark.isActive ? '' : 'black'}`"
                  >
                    Fase de Planejamento / Desenvolvimento Inicial
                  </span>
                </div>

                <p>Projeto em fase menos concreta, sem dados coletados.</p>

                <p>
                  A solicitação pode envolver mais trabalho de estruturação e
                  planejamento antes que a análise de dados possa realmente
                  começar.
                </p>
              </q-card-section>
            </q-card>
            <q-card
              :class="`${
                $q.dark.isActive
                  ? 'bg-[var(--q-dark-page)] hover:bg-slate-700 hover:cursor-pointer'
                  : 'bg-[var(--color-light-gray)] hover:bg-gray-200 hover:cursor-pointer'
              }`"
              class="w-full rounded-xl min-h-52 flex flex-col justify-between"
              @click="secondStep('R')"
            >
              <q-card-section
                class="flex flex-col justify-center gap-3 w-full flex-grow"
              >
                <div class="flex flex-col w-full my-2">
                  <span class="font-bold text-lg text-primary">
                    Dados já coletados
                  </span>
                  <span
                    class="font-semibold text-md"
                    :class="`${$q.dark.isActive ? '' : 'black'}`"
                  >
                    Foco na limpeza e interpretação dos dados
                  </span>
                </div>

                <p>
                  Projeto com dados já coletados e estão prontos para limpeza e
                  análise.
                </p>
                <p>
                  O objetivo principal deste tipo é limpar e analisar de
                  imediato, tornando o processo mais direto.
                </p>
              </q-card-section>
            </q-card>
            <q-card
              :class="`${
                $q.dark.isActive
                  ? 'bg-[var(--q-dark-page)] hover:bg-slate-700 hover:cursor-pointer'
                  : 'bg-[var(--color-light-gray)] hover:bg-gray-200 hover:cursor-pointer'
              }`"
              class="w-full rounded-xl min-h-52 flex flex-col justify-between"
              @click="secondStep('O')"
            >
              <q-card-section
                class="flex flex-col justify-center gap-3 w-full flex-grow"
              >
                <div class="flex flex-col w-full my-2">
                  <span class="font-bold text-lg text-primary"> Outros </span>
                  <span
                    class="font-semibold text-md"
                    :class="`${$q.dark.isActive ? '' : 'black'}`"
                  >
                    Qualquer solicitação que não se enquadre nas anteriores
                  </span>
                </div>

                <p>
                  Pode incluir situações específicas, análises ad hoc ou
                  projetos fora do padrão típico depesquisa.
                </p>
                <p>
                  Permite um tratamento mais personalizado e adaptativo do
                  fluxo.
                </p>
              </q-card-section>
            </q-card>
          </div>
        </q-step>
        <q-step
          :name="2"
          title="Dados básicos"
          icon="settings"
          :done="step > 2"
        >
          <q-form class="flex gap-3 w-full">
            <div
              class="grid grid-cols-1 lg:grid-cols-2 items-center justify-center gap-4 w-full"
            >
              <q-input
                :class="$q.dark.isActive ? '' : 'bg-white text-black'"
                outlined
                dense
                v-model="NovaSolicitacao.titulo"
                label="Título"
              />
              <q-input
                outlined
                dense
                :class="$q.dark.isActive ? '' : 'bg-white text-black'"
                v-model="NovaSolicitacao.link_dados"
                label="Link dos Dados"
              />
            </div>
            <div
              class="grid grid-cols-1 lg:grid-cols-1 items-center justify-center gap-4 w-full"
            >
              <q-input
                outlined
                dense
                type="textarea"
                :class="$q.dark.isActive ? '' : 'bg-white text-black'"
                v-model="NovaSolicitacao.objetivo_geral"
                label="Objetivo Geral"
              />
              <q-input
                outlined
                dense
                type="textarea"
                :class="$q.dark.isActive ? '' : 'bg-white text-black'"
                v-model="NovaSolicitacao.objetivos_especificos"
                label="Objetivos Específicos"
              />
            </div>
            <div class="grid grid-cols-2 items-center justify-end gap-4 w-full">
              <q-btn
                class="mt-2"
                color="white"
                text-color="primary"
                push
                rounded
                no-caps
                @click="firstStep()"
              >
                Voltar
              </q-btn>
              <q-btn
                class="mt-2"
                color="primary"
                push
                rounded
                no-caps
                @click="thirdStep()"
              >
                Continuar
              </q-btn>
            </div>
          </q-form>
        </q-step>
        <q-step
          :name="3"
          title="Dados opcionais"
          icon="settings"
          :done="step > 3"
        >
          <div
            class="grid grid-cols-1 lg:grid-cols-2 items-center justify-center gap-4 w-full"
          >
            <q-input
              v-for="(item, idx) in stepThreeFields"
              outlined
              dense
              :class="$q.dark.isActive ? 'mt-2' : 'bg-white text-black'"
              :key="idx"
              :label="item.label"
              :type="item.type || 'text'"
              v-model="NovaSolicitacao[item.model]"
            />
          </div>
          <q-checkbox
            right-label
            v-model="AcceptedTerms"
            label="Ao iniciar uma nova solicitação eu declaro que concordo com os Termos de Serviço da plataforma."
          />
          <div class="grid grid-cols-2 items-center justify-end gap-4 w-full">
            <q-btn
              class="mt-2"
              color="white"
              text-color="primary"
              push
              rounded
              no-caps
              @click="secondStep(NovaSolicitacao.tipo)"
            >
              Voltar
            </q-btn>
            <q-btn
              class="mt-2"
              color="primary"
              push
              rounded
              no-caps
              :disabled="!AcceptedTerms"
              @click="onClickCriarSolicitacao()"
            >
              Criar solicitação
            </q-btn>
          </div>
        </q-step>
      </q-stepper>
    </div>
  </q-page>
</template>

<script lang="ts">
interface NovaSolicitacaoType {
  [key: string]: string;
  titulo: string;
  link_dados: string;
  objetivo_geral: string;
  objetivos_especificos: string;
  hipoteses: string;
  variaveis: string;
  variaveis_dependentes: string;
  detalhamentos: string;
  instrumentos: string;
  instrumentos_referencias: string;
  metodologia: string;
  observacoes: string;
  tipo: string;
}

interface stepThreeFieldsSchema {
  label: string;
  model: keyof NovaSolicitacaoType; // Isso garante que o modelo se refere às chaves do objeto NovaSolicitacao,
  type: QInputTypes; // Restrição do tipo para os valores aceitos pelo QInput
  placeholder?: string;
}

type QInputTypes = 'number' | 'textarea' | 'email' | 'time' | 'text' | 'password' | 'search' | 'tel' | 'file' | 'url' | 'date' | 'datetime-local';





import { UserType, useUserStore } from 'src/stores/user';
import { useRoute, useRouter } from 'vue-router';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as SolicitacaoService from 'src/services/SolicitacaoService';
import * as UserService from 'src/services/UserService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData } from 'src/utils/dates';
import { useQuasar } from 'quasar';
import axios from 'axios';

import { initialEdges, initialNodes } from 'src/utils/initialElements';

export default defineComponent({
  name: 'SolicitacoesPage',
  setup() {
    const step = ref(1);
    const $q = useQuasar();
    const user = useUserStore();
    const route = useRoute();
    const router = useRouter();
    const User = ref<UserType>();
    const isColaborador = ref(false);
    const id = ref<string>();
    const isEditable = ref<boolean>();
    const flowLoaded = ref<boolean>();
    const AcceptedTerms = ref<boolean>(false);
    const visibleColumns = ref<string[]>();
    const dialogOrcamento = ref<boolean>();
    const dialogOrcamentoFinal = ref<boolean>();
    const Orcamento = ref({
      id: '',
      descricao: '',
      vencimento: '',
      valor: 0,
    });

    const OrcamentoFinal = ref({
      id: '',
      descricao: '',
      vencimento: '',
      valor: 0,
      entrega_link: '',
    });

    const Solicitacoes = ref([{ titulo: '' }]);

    const nodes = ref(initialNodes);
    const edges = ref(initialEdges);

    const updatePos = () => {
      nodes.value = nodes.value.map((node) => {
        return {
          ...node,
          position: {
            x: Math.random() * 400,
            y: Math.random() * 400,
          },
        };
      });
    };

    const elements = ref([
      {
        id: '1',
        position: { x: 50, y: 50 },
        label: 'Node 1',
      },
    ]);
    // watch(() => User.value, (newValue: UserType) => {
    //   if (!newValue) return
    //   isColaborador.value = newValue && (newValue.type == 'A' || newValue.type == 'C')
    // })

    const optionsTipo = ref([
      {
        label: 'Projeto de Pesquisa',
        value: 'P',
      },
      {
        label: 'Dados já coletados',
        value: 'R',
      },
      {
        label: 'Outro',
        value: 'O',
      },
    ]);

//     const stepThreeFields: { label: string; value: keyof NovaSolicitacaoType; type: QInputTypes }[] = [
//   { label: 'Variáveis', value: 'variaveis', type: 'text' },
//   { label: 'Variáveis Dependentes ou Desfecho', value: 'variaveis_dependentes', type: 'text' },
//   { label: 'Hipóteses', value: 'hipoteses', type: 'textarea' },
//   { label: 'Detalhamentos', value: 'detalhamentos', type: 'textarea' },
//   { label: 'Observações', value: 'observacoes', type: 'textarea' },
//   { label: 'Instrumentos', value: 'instrumentos', type: 'textarea' },
//   { label: 'Referências dos Instrumentos', value: 'instrumentos_referencias', type: 'textarea' },
//   { label: 'Metodologia', value: 'metodologia', type: 'textarea' },
// ];


const stepThreeFields = ref<stepThreeFieldsSchema[]>([
  { label: 'Variáveis', model: 'variaveis', type: 'text', placeholder: 'Digite as variáveis' },
  { label: 'Variáveis Dependentes ou Desfecho', model: 'variaveis_dependentes', type: 'text', placeholder: 'Digite as variáveis dependentes' },
  { label: 'Hipóteses', model: 'hipoteses', type: 'textarea', placeholder: 'Descreva as hipóteses' },
  { label: 'Detalhamentos', model: 'detalhamentos', type: 'textarea', placeholder: 'Detalhe o projeto' },
  { label: 'Observações', model: 'observacoes', type: 'textarea', placeholder: 'Observações adicionais' },
  { label: 'Instrumentos', model: 'instrumentos', type: 'textarea', placeholder: 'Descreva os instrumentos utilizados' },
  { label: 'Referências dos Instrumentos', model: 'instrumentos_referencias', type: 'textarea', placeholder: 'Referências dos instrumentos' },
  { label: 'Metodologia', model: 'metodologia', type: 'textarea', placeholder: 'Descreva a metodologia' },
]);

    const NovaSolicitacao = ref<NovaSolicitacaoType>({
      titulo: '',
      link_dados: '',
      objetivo_geral: '',
      objetivos_especificos: '',
      hipoteses: '',
      variaveis: '',
      variaveis_dependentes: '',
      detalhamentos: '',
      instrumentos: '',
      instrumentos_referencias: '',
      metodologia: '',
      observacoes: '',
      tipo: '',
      user_id: ''
    });

    const Usuarios = ref([{ 
      id: '',
      fullname: '',
      email: '',
      type: '',
      last_online: '',
      last_online_date: '',
      count_not_seen: 0
     }])

    const optionsUsers = ref([
      { label: 'Selecione...', value: '' },
    ]);

    const columns = ref([
      {
        name: 'data',
        label: 'Data',
        align: 'left',
        field: 'data',
        sortable: true,
      },
      {
        name: 'titulo',
        label: 'Título',
        align: 'left',
        field: 'titulo',
        sortable: true,
      },
      {
        name: 'status',
        label: 'Status',
        align: 'center',
        field: 'status',
        sortable: true,
      },
      {
        name: 'actions',
        label: 'Ações',
        align: 'right',
        field: 'actions',
        sortable: false,
      },
    ]);

    watch(
      () => user.userData,
      (newValue: UserType) => {
        User.value = newValue;
      }
    );

    const getUsuarios = async () => {
      if (User?.value?.type == 'U'){
        optionsUsers.value.push({
          label: User.value.fullname,
          value: User.value.id
        })
        return
      }
      $q.loading.show({ message: 'Buscando usuários' })
      const response = await UserService.Get()
      if (response.data){
        Usuarios.value = response.data
      } else {
        triggerNegative('Houve um erro ao buscar os usuários')
      }
      optionsUsers.value = []
      for (let User of Usuarios.value){
        optionsUsers.value.push({
          label: User.fullname,
          value: User.id
        })
      }
      $q.loading.hide()
    }

    // const getSolicitacao = async () => {
    //   flowLoaded.value = false
    //   for (let node of initialNodes){
    //     node.class = ''
    //   }
    //   let nodeId = ''

    //   $q.loading.show({ message: 'Buscando solicitação' })
    //   const response = await SolicitacaoService.GetOne(id.value)
    //   if (axios.isAxiosError(response)){
    //     triggerNegative(response.response?.data.message)
    //     router.push('/solicitacoes')
    //     return
    //   }
    //   if (response.data){
    //     NovaSolicitacao.value = response.data
    //     isEditable.value = false
    //     switch (NovaSolicitacao.value.status) {
    //       case 'AGUARDANDO_ANALISTA':
    //         isEditable.value = true
    //         nodeId = 'analise-1'
    //         break;
    //       case 'VERIFICANDO_SOLICITACAO':
    //         nodeId = 'analise-1'
    //         break;
    //       case 'EM_ANALISE':
    //         nodeId = 'analise-2'
    //         break;
    //       case 'AGUARDANDO_REUNIAO':
    //         nodeId = 'reuniao'
    //         break;
    //       case 'CANCELADO':
    //         nodeId = 'cancelado-atraso'
    //         break;
    //       case 'AGUARDANDO_PAGAMENTO':
    //         nodeId = 'orcamento-1'
    //         break;
    //         case 'ATRASO':
    //           nodeId = 'orcamento-1'
    //           break;
    //       case 'AGUARDANDO_PAGAMENTO_FINAL':
    //         nodeId = 'pagamento-2'
    //         break;
    //       case 'ATRASO_FINAL':
    //         nodeId = 'pagamento-2'
    //         break;
    //       case 'FINALIZADA':
    //         nodeId = 'finalizado'
    //         break;

    //       default:
    //         break;
    //     }
    //   } else {
    //     triggerNegative('Houve um erro ao buscar a solicitação')
    //   }
    //   for (let node of initialNodes){
    //     node.class = ''
    //     if (node.id === nodeId){
    //       node.class = 'etapa_atual'
    //     }
    //   }
    //   flowLoaded.value = true
    //   $q.loading.hide()
    // }

    onMounted(async () => {
      User.value = await user.storeUserDataGetter;
      NovaSolicitacao.value.user_id = User.value.id
      getUsuarios()
      const idRoute = route.params.id;
      if (typeof idRoute === 'string') {
        if (idRoute != 'create') {
          id.value = idRoute;
          // getSolicitacao()
        } else {
          for (let node of initialNodes) {
            if (node.id === 'cadastro') {
              node.class = 'etapa_atual';
            }
          }
          flowLoaded.value = true;
        }
      }
      if ($q.screen.width <= 600) {
        //   visibleColumns.value = ['titulo', 'status', 'data']
        //   columns.value = [
        //     { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
        //     { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
        //     { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
        //     { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        //   ]
        // } else {
        //   visibleColumns.value = ['titulo', 'status', 'data']
        //   columns.value = [
        //     { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDateApenasData(row.xata.createdAt) }, sortable: true },
        //     { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
        //     { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
        //     { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
        //   ]
      }
    });

    const firstStep = async () => {
      step.value = 1;
    };
    const secondStep = async (option: string) => {
      NovaSolicitacao.value.tipo = option;
      step.value = 2;
    };

    /**
     * Go to the third step of the wizard.
     * @async
     */
    const thirdStep = async () => {
      step.value = 3;
    };

    const onClickVoltar = () => {
      if (isColaborador.value) {
        router.push('/solicitacoes_colaborador');
      } else {
        router.push('/solicitacoes');
      }
    };

    const onClickCriarSolicitacao = async () => {
      console.log(NovaSolicitacao.value);

      $q.loading.show({ message: 'Criando Solicitação' });
      try {
        if (!NovaSolicitacao.value.titulo) {
          triggerNegative('Informe o título da solicitação');
          return;
        }
        if (!NovaSolicitacao.value.tipo) {
          triggerNegative('Informe o tipo da solicitação');
          return;
        }
        if (!AcceptedTerms.value) {
          triggerNegative(
            'É necessário aceitar os termos de serviço da plataforma'
          );
          return;
        }
        const response = await SolicitacaoService.Criar(NovaSolicitacao.value);
        if (axios.isAxiosError(response)) {
          triggerNegative(response.response.data.message)
        } else {
          if (response.data) {
            triggerSuccess(response.data);
          }
          router.push('/solicitacoes');
          return;
        }
      } catch (error) {
        console.log(error);
      } finally {
        $q.loading.hide();
      }
    };

    return {
      User,
      optionsUsers,
      formatDate,
      formatDateApenasData,
      isEditable,
      visibleColumns,
      Solicitacoes,
      NovaSolicitacao,
      columns,
      optionsTipo,
      AcceptedTerms,
      router,
      isColaborador,
      onClickVoltar,
      dialogOrcamento,
      dialogOrcamentoFinal,
      Orcamento,
      OrcamentoFinal,
      elements,
      edges,
      nodes,
      updatePos,
      flowLoaded,
      step,
      firstStep,
      secondStep,
      thirdStep,
      onClickCriarSolicitacao,
      stepThreeFields,
    };
  },
});
</script>

<style>
.etapa_atual {
  background: rgb(153 0 204);
  color: white;
  border: 1px solid rgb(153 0 204);
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgb(153 0 204);
  padding: 8px;
}
</style>
