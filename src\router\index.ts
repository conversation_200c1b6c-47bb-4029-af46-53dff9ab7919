import { route } from 'quasar/wrappers';
import {
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';

import routes from './routes';
// import { useAuthStore } from 'src/stores/auth';

export const isAuthenticated = (): boolean => {
  return true;
  // const auth = useAuthStore();
  // return auth.authData.token ? true : false;
};

export const hasPermissionsNeeded = () => {
  return true;
};

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = import.meta.env.SERVER
    ? createMemoryHistory
    : import.meta.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(import.meta.env.VUE_ROUTER_BASE),
  });

  Router.beforeEach((to, from, next) => {
    // Lista de rotas públicas que não precisam de autenticação
    const publicRoutes = [
      'login',
      'forgotpass',
      'cadastro',
      'unauthorized',
      'notfound',
      'loginAD',
      'biblioteca', // Adicionando biblioteca como rota pública
    ];

    // Lista de rotas que são sempre públicas (incluindo home e biblioteca)
    const alwaysPublicRoutes = ['home', 'biblioteca'];

    if (
      typeof to.name === 'string' &&
      publicRoutes.includes(to.name)
    ) {
      // Se é uma rota pública e o usuário está logado, redireciona para dashboard
      // Exceto para biblioteca que deve ser sempre acessível
      if (to.name === 'biblioteca') {
        next();
      } else {
        isAuthenticated() ? next('/dashboard') : next();
      }
    } else {
      // Para outras rotas, verifica autenticação ou se é uma rota sempre pública
      isAuthenticated() || alwaysPublicRoutes.includes(to.name as string)
        ? next()
        : next('/ErrorUnauthorized');
    }
  });

  return Router;
});
