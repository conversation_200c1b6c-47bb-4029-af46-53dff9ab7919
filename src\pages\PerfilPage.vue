<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3" v-if="User">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]">
        <p class="text-lg mt-2 font-league font-bold">PERFIL</p>
        <q-btn
            flat
            :label="isEditable ? 'Fechar' : 'Editar'"
            :icon="isEditable ? 'close' : 'edit'"
            :color="isEditable ? 'secondary' : 'primary'"
            class='ml-10 bg-primary text-white mb-3'
            @click="isEditable = !isEditable"/>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]">

        <q-input
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          v-model="User.email"
          label="E-mail"
          disable
        />

        <q-input
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          v-model="User.fullname"
          label="Nome"
          :disable="!isEditable"
        />

        <q-input
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          v-model="User.cgc"
          label="CPF"
          :disable="!isEditable"
        />

        <q-input
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          v-model="User.cellphone_number"
          label="Telefone"
          :disable="!isEditable"
        />

        <q-input
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          v-model="User.cellphone_number_aux"
          label="Telefone 2"
          :disable="!isEditable"
        />

        <q-select
          emit-value
          map-options
          transition-show="jump-up"
          transition-hide="jump-up"
          v-model="User.gender"
          :options="optionsGender"
          :disable="!isEditable"
          :filled="!isEditable"
          :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
          label="Gênero"
          outlined
          dense
        />

        <div class="flex flex-nowrap justify-end mt-2">
          <q-btn label="Salvar" type="submit" color="primary" class="px-10" rounded @click="onClickSave"/>
        </div>
      </div>
    </div>
   </q-page>
</template>

<script lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as UserService from 'src/services/UserService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { useQuasar } from 'quasar';
import axios from 'axios';

export default defineComponent({
  name: 'PerfilPage',
  components: {  },
  setup () {
    const $q = useQuasar()
    const user = useUserStore();
    const User = ref<UserType>()
    const isEditable = ref<false>()

    const optionsGender = ref([
        {
          label: 'Feminino',
          value: 'F',
        },
        {
          label: 'Masculino',
          value: 'M',
        },
        {
          label: 'Outro',
          value: 'O',
        },
        {
          label: 'Prefiro não informar',
          value: null,
        }
      ])
    
    const route = useRoute()
    const router = useRouter()
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    const getUser = async (id: string) => {
      const UsuarioLogado = await user.storeUserDataGetter
      if (UsuarioLogado.id === id) {
        User.value = UsuarioLogado
        return
      }
      if (UsuarioLogado.type != 'A' && UsuarioLogado.type != 'C'){
        router.push('/')
        return
      }
      const response = await UserService.GetUser(id)
      User.value = response.data
    }

    onMounted( async () => {
      getUser(`${route.params.id}`)
    })

    const onClickSave = async () => {
      $q.loading.show();
      try {
        if (User.value){
          const response = await UserService.Update(User.value.id, User.value)
          if (axios.isAxiosError(response)) {
            if (response.response.data.message){
              triggerNegative(response.response.data.message);
            } else {
              triggerNegative('Houve um erro ao excluir itens');
            }
            return
          }
          isEditable.value = false
          triggerSuccess('Perfil atualizado')
        } else {
          triggerNegative('Houve um erro ao atualizar o perfil.')
        }

      } catch (error) {
        console.log(error)
      } finally {
        $q.loading.hide();
        return
      }
    }

    return { 
      User,
      onClickSave,
      isEditable,
      optionsGender,
    };


  }
});
</script>

<style scoped>
.imagem {
    background-image: url("../assets/backgroundPeoesPonte.png");
    background-color: #fff;
    color: #000;
    background-size: 100%;
    opacity: 0.1;
  }

.demo-wrap {
  overflow: hidden;
  position: relative;
}

.demo-bg {
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
}

.demo-content {
  position: relative;
}

.first-wrap {
  overflow: hidden;
  position: relative;
}

.first-bg {
  opacity: 0;
  margin-left: auto;
  margin-right: auto;
  width: 90%;
  height: auto;
  display: block;
}

.first-content {
  position: relative;
}
</style>