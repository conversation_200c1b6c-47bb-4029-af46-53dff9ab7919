<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">
    <div class="p-3 gap-3" v-if="User">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold">SOLICITAÇÕES</p>
        <div class="text-center">
          <q-btn rounded color="primary" icon="add" label="Nova Solicitação" @click="router.push('/nova-solicitacao')"/>
        </div>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]">
        <q-table
          :rows="Solicitacoes"
          :columns="columns"
          :visible-columns="visibleColumns"
          row-key="name"
          :pagination="{rowsPerPage: 50}"
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="data" v-if="props.row.xata">
                {{ formatDate(props.row.xata.createdAt) }}
              </q-td>
              <q-td v-else></q-td>
              <q-td key="titulo" class="text-left" v-if="!visibleColumns?.indexOf('titulo')">
                {{ props.row.titulo }}
              </q-td>
              <q-td key="usuario" class="text-left" v-if="props.row.usuario">
                {{ props.row.usuario.fullname || 'Nome' }}
              </q-td>
              <q-td key="valor_total" class="text-center">
                <q-badge v-if="props.row.status === 'AGUARDANDO_ANALISTA'" class="p-2" rounded color="green" label="Aguardando Analista" />
                <q-badge v-if="props.row.status === 'VERIFICANDO_SOLICITACAO'" class="p-2" rounded text-color="black" color="yellow" label="Verificando Solicitação" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_REUNIAO'" class="p-2" rounded color="orange" label="Aguardando Reunião" />
                <q-badge v-if="props.row.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_PAGAMENTO'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="props.row.status === 'AGUARDANDO_PAGAMENTO_FINAL'" class="p-2" rounded color="orange" label="Aguardando Pagamento" />
                <q-badge v-if="props.row.status === 'FINALIZADA'" class="p-2" rounded color="blue" label="Finalizada" />
                <q-badge v-if="props.row.status === 'EM_ANALISE'" class="p-2" rounded text-color="black" color="yellow" label="Em análise" />
                <q-badge v-if="props.row.status === 'ATRASO_FINAL'" class="p-2" rounded color="red" label="Em atraso" />
                <q-badge v-if="props.row.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
              </q-td>
              <q-td key="actions" class="text-center">
                <q-btn rounded color="primary" icon="search" @click="onClickDetalhes(props.row)">
                </q-btn>
              </q-td>
              <!--<q-td key="status" class="text-center">
                <q-btn disable v-if="props.row.status === 'AP'" class="bg-green-700 text-white w-full">
                    Aguardando Pagamento
                </q-btn>
                <q-btn disable v-if="props.row.status === 'P'" class="bg-green-500 text-white w-full">
                    Pago
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'F'" class="bg-blue-500 text-white w-full">
                    Finalizado
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'A'" class="bg-yellow-500 text-white w-full">
                    Aberto
                </q-btn>
                <q-btn disable v-else-if="props.row.status === 'C'" class="bg-red-700 text-white w-full">
                    Cancelado
                </q-btn>
              </q-td> -->
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="full-width row flex-center q-gutter-sm">
              <q-icon size="2em" name="sentiment_dissatisfied" />
              <span>
                Você ainda não tem solicitações.
              </span>
            </div>
          </template>
        </q-table>
    </div>
    <q-dialog v-model="dialogDetalhes" full-width>
      <q-card class="items-center w-full" v-if="SolicitacaoSelecionada">
          <q-card-section>
            <div class="row">
              <div>
                <div class="text-h6" align="left">Orçamento da Solicitação {{SolicitacaoSelecionada.titulo}}</div>
              </div>
            </div>
          </q-card-section>
          <q-card-section class="flex flex-col gap-2">
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.usuario.fullname"
              label="Usuário"
              disable
            />
            <q-input v-if="SolicitacaoSelecionada.colaborador"
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.colaborador.fullname"
              label="Analista responsável"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.titulo"
              label="Título"
              disable
            />
            <q-select
              emit-value
              map-options
              transition-show="jump-up"
              transition-hide="jump-up"
              v-model="SolicitacaoSelecionada.tipo"
              :options="optionsTipo"
              :disable="!isEditable"
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black mt-2') : 'bg-inherit mt-2'"
              label="Gênero"
              outlined
              dense
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.link_dados"
              label="Link dos Arquivos"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.variaveis"
              label="Variáveis"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.objetivo_geral"
              label="Objetivo Geral"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.objetivos_especificos"
              label="Objetivos Específicos"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.hipoteses"
              label="Hipóteses"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.detalhamentos"
              label="Detalhamentos"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.observacoes"
              label="Observações"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.instrumentos"
              label="Instrumentos"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.instrumentos_referencias"
              label="Referências dos Instrumentos"
              disable
            />
            <q-input
              :filled="!isEditable"
              :class="isEditable ? ($q.dark.isActive ? 'mt-2' : 'bg-white text-black') : 'bg-inherit'"
              v-model="SolicitacaoSelecionada.metodologia"
              label="Metodologia"
              disable
            />
          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-card-actions>
              <q-btn
                push
                color="secondary"
                text-color="white"
                :label="'Voltar'"
                @click="dialogDetalhes = false"
              />
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Apropriar'"
                v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_ANALISTA'"
                @click="onClickApropriar"
              />
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Aguardando Reunião'"
                v-if="SolicitacaoSelecionada.status === 'VERIFICANDO_SOLICITACAO'"
                @click="onClickAguardandoReuniao"
              />
              <q-btn
                push
                color="primary"
                text-color="white"
                :label="'Cancelar'"
                v-if="SolicitacaoSelecionada.status === 'AGUARDANDO_REUNIAO' || SolicitacaoSelecionada.status === 'ATRASO' || SolicitacaoSelecionada.status === 'ATRASO_FINAL'"
                @click="onClickCancelar"
              />
            </q-card-actions>
          </q-card-section>
      </q-card>
    </q-dialog>
    </div>
  </q-page>
</template>

<script lang="ts">
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as SolicitacaoService from 'src/services/SolicitacaoService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import axios from 'axios';

export default defineComponent({
  name: 'SolicitacoesColaboradorPage',
  components: {  },
  setup () {
    const $q = useQuasar()
    const router = useRouter()
    const user = useUserStore();
    const User = ref<UserType>()
    const qrcode = ref<''>()
    const dialogDetalhes = ref<boolean>()
    const isEditable = ref<boolean>()
    const visibleColumns = ref<string[]>()

    const optionsTipo = ref([
        {
          label: 'Projeto de Pesquisa',
          value: 'P',
        },
        {
          label: 'Dados já coletados',
          value: 'R',
        },
        {
          label: 'Outro',
          value: 'O',
        }
      ])
    

    const Solicitacoes = ref([{ titulo: '' }])
    const SolicitacaoSelecionada = ref(
      {
        colaborador: {
          fullname: ''
        },
        detalhamentos: '',
        entrega_link: '',
        hipoteses: '',
        id: '',
        instrumentos: '',
        instrumentos_referencias: '',
        link_dados: '',
        metodologia: '',
        objetivo_geral: '',
        objetivos_especificos: '',
        observacoes: '',
        status: '',
        tipo: '',
        titulo: '',
        usuario: {
          id: '',
          fullname: ''
        },
        variaveis: '',
        viabilidade_descricao: '',
        viabilidade_link: '',
        viabilidade_prazo: '',
        viabilidade_valor: '',
        xata: {
          createdAt: ''
        }
      }
    )
    const columns = ref([
      { name: 'data', label: 'Data', align: 'left', field: 'data', sortable: true },
      { name: 'titulo', label: 'Título', align: 'left', field: 'titulo', sortable: true },
      { name: 'usuario', label: 'Usuário', align: 'left', field: 'usuario.fullname', sortable: true },
      { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
      { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
    ])
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    const formatDate = (originalDate) => {
      // Criar um objeto de data com base na string original
      const dataObjeto = new Date(originalDate);

      // Obter componentes da data
      const dia = dataObjeto.getUTCDate();
      const mes = dataObjeto.getUTCMonth() + 1; // Mês é base 0, então adicionamos 1
      const hora = dataObjeto.getUTCHours();
      const minuto = dataObjeto.getUTCMinutes();

      // Formatar a nova string
      const novaString = `${dia < 10 ? '0' : ''}${dia}/${mes < 10 ? '0' : ''}${mes} ${hora}:${minuto}`;

      return novaString
    }
    const formatDateApenasData = (originalDate) => {
      // Criar um objeto de data com base na string original
      const dataObjeto = new Date(originalDate);

      // Obter componentes da data
      const dia = dataObjeto.getUTCDate();
      const mes = dataObjeto.getUTCMonth() + 1; // Mês é base 0, então adicionamos 1

      // Formatar a nova string
      const novaString = `${dia < 10 ? '0' : ''}${dia}/${mes < 10 ? '0' : ''}${mes}`;

      return novaString
    }

    const getSolicitacoes = async () => {
      $q.loading.show({ message: 'Buscando solicitações' })
      const response = await SolicitacaoService.Get()
      if (response.data){
        Solicitacoes.value = response.data
        await formatSolicitacoes()
      } else {
        triggerNegative('Houve um erro ao buscar as solicitações')
      }
      $q.loading.hide()
    }

    const formatSolicitacoes = async () => {
      for (let Solicitacao of Solicitacoes.value){
        if (Solicitacao.titulo.length >= 75){
          Solicitacao.titulo = `${Solicitacao.titulo.substring(0, 75)}...`
        }
      }
    }

    onMounted( async () => {
      User.value = await user.storeUserDataGetter
      if (User.value.type != 'A' && User.value.type != 'C'){
        triggerNegative('Usuário sem permissão para acessar esta página')
        router.push('/')
      }
      getSolicitacoes()
      if ($q.screen.width <= 600){
        visibleColumns.value = ['titulo', 'status', 'data', 'usuario']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDate(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'usuario', align: 'left', label: 'Usuário', field: 'usuario.fullname', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
          // { name: 'total', align: 'right', label: 'Total', field: 'total', sortable: true },
          // { name: 'status', align: 'center', label: 'Status', field: 'status', sortable: true },
        ]
      } else {
        visibleColumns.value = ['titulo', 'status', 'data', 'usuario']
        columns.value = [
          { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDate(row.xata.createdAt) }, sortable: true },
          { name: 'titulo', align: 'left', label: 'Título', field: 'titulo', sortable: true },
          { name: 'usuario', align: 'left', label: 'Usuário', field: 'usuario.fullname', sortable: true },
          { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
          { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
          // { name: 'data', label: 'Data', align: 'left', field: (row: { xata: { createdAt: any }; }) => { return formatDate(row.xata.createdAt) }, sortable: true },
          // { name: 'total', align: 'right', label: 'Total', field: 'total', sortable: true },
          // { name: 'status', align: 'center', label: 'Status', field: 'status', sortable: true },
        ]
      }
    })

    const onClickDetalhes = async (Solicitacao: any) => {
      router.push(`/solicitacao/${Solicitacao.id}`)
    }

    const onClickApropriar = async() => {
      $q.loading.show({ message: 'Apropriando Solicitação' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Apropriar(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao apropriar a solicitação')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        dialogDetalhes.value = false
        $q.loading.hide()
        getSolicitacoes()
      }
    }
    const onClickCancelar = async() => {
      $q.loading.show({ message: 'Cancelando Solicitação' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Cancelar(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao cancelar a solicitação')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        dialogDetalhes.value = false
        $q.loading.hide()
        getSolicitacoes()
      }
    }

    const onClickAguardandoReuniao = async() => {
      $q.loading.show({ message: 'Reunião' })
      try {
        if (!SolicitacaoSelecionada.value) return
        const response = await SolicitacaoService.Reuniao(SolicitacaoSelecionada.value.id)
        if (axios.isAxiosError(response)){
          triggerNegative('Houve um erro ao agendar reunião')
        } else {
          if (response.data){
            triggerSuccess(response.data)
          }          
        }
      } catch (error) {
        console.log(error)
      } finally {
        dialogDetalhes.value = false
        $q.loading.hide()
        getSolicitacoes()
      }
    }

    return { 
      User,
      qrcode,
      router,
      formatDate,
      formatDateApenasData,
      onClickDetalhes,
      onClickAguardandoReuniao,
      onClickApropriar,
      onClickCancelar,
      dialogDetalhes,
      SolicitacaoSelecionada,
      visibleColumns,
      Solicitacoes,
      columns,
      isEditable,
      optionsTipo,
     };


  }
});
</script>

<style scoped>

</style>