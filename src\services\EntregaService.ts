import { instance } from 'src/services/Interceptor/interceptor';
import { environment } from 'src/environment/environment';

export interface EntregaData {
  url: string;
  customer_name: string;
}

export interface Entrega {
  code: string;
  customer_name: string;
  id: string;
  unlocked: boolean;
  url: string;
  user_id: {
    email: string;
    fullname: string;
    id: string;
    xata: {
      createdAt: string;
      updatedAt: string;
      version: number;
    };
  };
  xata: {
    createdAt: string;
    updatedAt: string;
    version: number;
  };
}

export function Get() {
  return instance.get(`${environment.baseUrl}/entregas`);
}

export function Entregar(data: EntregaData) {
  return instance.post(`${environment.baseUrl}/entregas`, data);
}
