<template>
  <q-layout view="hHh lpR fFf">
    <q-header :class="$q.dark.isActive ? 'bg-dark text-white' : 'bg-white text-dark'" bordered >
      <q-toolbar class="align-middle items-center">
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
          v-if="isLogged"
        />

         <img
          @click="router.push('/')"
          :src="$q.dark.isActive ? '/icons/logo_white_purple.png' : '/icons/logo_black.png'"
          alt="logo"
          class="q-ml-md"
          style="max-width: 25px; cursor: pointer"
          />

        <q-toolbar-title v-if="!($q.screen.width < 400)" class="font-league font-bold mt-1" @click="router.push('/')" style="cursor: pointer">
          <p v-if="$q.screen.gt.sm">HABILIS.TECH</p>
          <p v-else>HABILIS</p>
        </q-toolbar-title>
        <div class="flex items-center gap-2">
          <button
            @click="toggleLanguage('pt')"
            class="w-8 h-8 opacity-50 hover:opacity-100 transition-opacity flex items-center justify-center"
            :class="{ '!opacity-100': locale === 'pt' }"
          >
            <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/br.svg" alt="Português" class="w-full h-full" />
          </button>
          <button
            @click="toggleLanguage('en')"
            class="w-8 h-8 opacity-50 hover:opacity-100 transition-opacity flex items-center justify-center"
            :class="{ '!opacity-100': locale === 'en' }"
          >
            <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/us.svg" alt="English" class="w-full h-full" />
          </button>
        
          <div v-if="isLogged" class="flex ms-auto" >
            <!-- colocar foto do perfil -->
          </div>
          <q-btn v-if="User?.id"
            rounded
            flat
            class="q-ml-md"
            :label="`Olá, ${User.fullname}`"
            :color="$q.dark.isActive ? 'dark' : 'white'"
            :text-color="$q.dark.isActive ? 'white' : 'black'"
            icon="person"
            @click="router.push('/perfil')"
          />
          <!-- <q-btn
            rounded
            flat
            class="q-ml-md"
            :color="$q.dark.isActive ? 'dark' : 'white'"
            :text-color="$q.dark.isActive ? 'white' : 'black'"
            :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'"
            @click="toggleDarkMode"
          /> -->
          <q-btn
            push
            class="q-ml-md flex ms-auto ml-2"
            icon-right="login"
            :label="!$q.screen.lt.sm ? 'Login' : ''"
            padding="xs lg"
            color="primary"
            :disable="!backendUp"
            @click="toggleDialogLogin"
            v-if="!isLogged"
          />
          <q-btn
            push
            class="q-ml-md"
            icon-right="logout"
            :label="!$q.screen.lt.sm ? 'Logout' : ''"
            padding="xs lg"
            color="primary"
            :disable="!backendUp"
            @click="onClickLogout"
            v-else
          />
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      v-if="isLogged"
      side="left"
      bordered
      show-if-above
      class="flex items-start w-full h-full justify-center"
    >
      <q-scroll-area style="height: 80vh; width: 275px" class="row" >

      <q-list class="" v-if="!isLogged">
        <EssentialLink
        v-for="link in linksList"
        :key="link.title"
        v-bind="link"
        />
      </q-list>
      <q-list class="" v-else-if="isColaborador">
        <EssentialLink
          v-for="link in linksColaborador"
          :key="link.title"
          v-bind="link"
        />
      </q-list>
      <q-list class="" v-else-if="linksLogged">
        <EssentialLink
          v-for="link in linksLogged"
          :key="link.title"
          v-bind="link"
        />
      </q-list>

      <div class="flex justify-center row">
        <!-- <q-btn
          rounded
          class="flex items-center q-mt-lg row w-11/12"
          color="primary"
          :label="$q.dark.isActive ? 'Light' : 'Dark'"
          :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'"
          @click="toggleDarkMode"
        /> -->
        <q-btn
          rounded
          class="flex items-center q-mt-lg row w-11/12"
          color="green-6"
          :label="$q.dark.isActive ? 'Chat' : 'Chat'"
          :icon="$q.dark.isActive ? 'chat' : 'chat'"
          @click="toggleDialogChat"
        >
          <q-inner-loading :showing="!socketConnected">
            <q-spinner-gears size="50px" color="white" />
          </q-inner-loading>
        </q-btn>
        <q-btn
          v-if="isLogged"
          rounded
          outline
          icon-right="logout"
          class="flex items-center q-mt-lg row w-11/12"
          label="Logout"
          :color="$q.dark.isActive ? 'white' : 'primary'"
          no-caps
          @click="onClickLogout"
        />
      </div>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>

    <q-dialog v-model="dialogLogin" transition-show="jump-down" transition-hide="jump-up">
      <div class="login-form-container" :class="$q.dark.isActive ? 'bg-dark' : 'bg-white text-black'">
        <p class="login-title">Login</p>
        <form @submit.prevent="onSubmit" class="login-form" :class="$q.dark.isActive ? 'bg-dark' : 'bg-white text-black'">
            <div class="login-input-group">
                <label for="email">E-mail</label>
                <q-input v-model="formData.email" outlined/>
            </div>
            <div class="login-input-group">
                <label for="senha">Senha</label>
                <q-input v-model="formData.password" :type="isPwd ? 'password' : 'text'" outlined>
                  <template v-slot:append>
                    <q-icon
                      :name="isPwd ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="isPwd = !isPwd"
                      size="15px"
                    />
                  </template>
                </q-input>
                <div class="login-forgot">
                    <a rel="noopener noreferrer" :class="$q.dark.isActive ? 'text-white' : 'text-black'" @click="onClickSenha">Esqueceu sua senha?</a>
                </div>
            </div>
            <button class="login-sign" type="submit">Entrar</button>
        </form>
        <div class="login-social-message">
          <div class="login-line"></div>
          <p class="login-message">ou</p>
          <div class="login-line"></div>
        </div>
        <div class="flex-row">
          <button class="btn google" @click="onClickLoginGoogle">
            <svg version="1.1" width="20" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
              <path style="fill:#FBBB00;" d="M113.47,309.408L95.648,375.94l-65.139,1.378C11.042,341.211,0,299.9,0,256
              c0-42.451,10.324-82.483,28.624-117.732h0.014l57.992,10.632l25.404,57.644c-5.317,15.501-8.215,32.141-8.215,49.456
              C103.821,274.792,107.225,292.797,113.47,309.408z"></path>
              <path style="fill:#518EF8;" d="M507.527,208.176C510.467,223.662,512,239.655,512,256c0,18.328-1.927,36.206-5.598,53.451
              c-12.462,58.683-45.025,109.925-90.134,146.187l-0.014-0.014l-73.044-3.727l-10.338-64.535
              c29.932-17.554,53.324-45.025,65.646-77.911h-136.89V208.176h138.887L507.527,208.176L507.527,208.176z"></path>
              <path style="fill:#28B446;" d="M416.253,455.624l0.014,0.014C372.396,490.901,316.666,512,256,512
              c-97.491,0-182.252-54.491-225.491-134.681l82.961-67.91c21.619,57.698,77.278,98.771,142.53,98.771
              c28.047,0,54.323-7.582,76.87-20.818L416.253,455.624z"></path>
              <path style="fill:#F14336;" d="M419.404,58.936l-82.933,67.896c-23.335-14.586-50.919-23.012-80.471-23.012
              c-66.729,0-123.429,42.957-143.965,102.724l-83.397-68.276h-0.014C71.23,56.123,157.06,0,256,0
              C318.115,0,375.068,22.126,419.404,58.936z"></path>
              </svg>
              <a rel="noopener noreferrer" :class="$q.dark.isActive ? 'text-black' : 'text-black'"> Entrar com Google</a>
          </button>
        </div>
        <p class="login-signup">Ainda não tem conta?
            <a rel="noopener noreferrer" @click="onClickRegister" :class="$q.dark.isActive ? 'text-white' : 'text-black'"> Cadastre-se</a>
        </p>
    </div>
    </q-dialog>
    <q-dialog v-model="dialogChat" transition-show="jump-down" transition-hide="jump-up">
      <q-card style="width: 700px; max-width: 80vw;">
        <q-card-section>
          <div class="text-h6">Fale conosco!</div>
        </q-card-section>
        <q-card-section v-if="chatMessages.length > 0">
          <q-scroll-area style="height: 400px; max-width: 650px;">
          <div>
            <q-chat-message v-for="(message, index) in chatMessages" :key="index"
              :name="message.collab ? message.collab.fullname : message.user.fullname"
              :text="[message.message]"
              :sent="message.collab ? false : true"
              :stamp="message.xata.stamp"
              :bg-color="message.collab ? 'grey-5' : 'green-4'"
            >
            </q-chat-message>
          </div>
          </q-scroll-area>
        </q-card-section>
        <q-card-section>
          <div class="row gap-2">
            <q-input class="col col-10 grow" v-model="chatMessageInput" :label="'Escreva sua mensagem'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="text" size="15px" />
              </template>
            </q-input>
            <q-btn
              push
              class="col col-1"
              color="primary"
              text-color="white"
              icon="send"
              no-caps
              @click="onClickSendMessage"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
    <q-dialog v-model="dialogRegister" transition-show="jump-down" transition-hide="jump-up">
      <q-card style="width: 70vw">
        <q-card-section>
          <div class="text-h6">Cadastro</div>
        </q-card-section>
        <q-card-section>
          <div class="flex-row">
            <button class="btn google" @click="onClickLoginGoogle">
              <svg version="1.1" width="20" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
                <path style="fill:#FBBB00;" d="M113.47,309.408L95.648,375.94l-65.139,1.378C11.042,341.211,0,299.9,0,256
                c0-42.451,10.324-82.483,28.624-117.732h0.014l57.992,10.632l25.404,57.644c-5.317,15.501-8.215,32.141-8.215,49.456
                C103.821,274.792,107.225,292.797,113.47,309.408z"></path>
                <path style="fill:#518EF8;" d="M507.527,208.176C510.467,223.662,512,239.655,512,256c0,18.328-1.927,36.206-5.598,53.451
                c-12.462,58.683-45.025,109.925-90.134,146.187l-0.014-0.014l-73.044-3.727l-10.338-64.535
                c29.932-17.554,53.324-45.025,65.646-77.911h-136.89V208.176h138.887L507.527,208.176L507.527,208.176z"></path>
                <path style="fill:#28B446;" d="M416.253,455.624l0.014,0.014C372.396,490.901,316.666,512,256,512
                c-97.491,0-182.252-54.491-225.491-134.681l82.961-67.91c21.619,57.698,77.278,98.771,142.53,98.771
                c28.047,0,54.323-7.582,76.87-20.818L416.253,455.624z"></path>
                <path style="fill:#F14336;" d="M419.404,58.936l-82.933,67.896c-23.335-14.586-50.919-23.012-80.471-23.012
                c-66.729,0-123.429,42.957-143.965,102.724l-83.397-68.276h-0.014C71.23,56.123,157.06,0,256,0
                C318.115,0,375.068,22.126,419.404,58.936z"></path>
                </svg>
                <a rel="noopener noreferrer" :class="$q.dark.isActive ? 'text-black' : 'text-black'"> Cadastre-se com Google</a>
            </button>
          </div>
          <div class="login-social-message">
            <div class="login-line"></div>
            <p class="login-message">ou</p>
            <div class="login-line"></div>
          </div>
        </q-card-section>
        <q-card-section>
          <q-form @submit.prevent="onClickSubmitRegister" class="q-gutter-md" >
            <q-input v-model="registerData.email" :label="'E-mail (será sua forma de realizar o Login)'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="mail" size="15px" />
              </template>
            </q-input>

            <q-input v-model="registerData.fullname" :label="'Nome'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="person" size="15px" />
              </template>
            </q-input>

            <q-input v-model="registerData.cellphone_number" :label="'Telefone'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="phone" size="15px" />
              </template>
            </q-input>

            <q-input v-model="registerData.password" :label="'Senha'" :type="isPwd ? 'password' : 'text'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="lock" size="15px" />
              </template>
              <template v-slot:append>
                <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" size="15px" />
              </template>
            </q-input>

            <q-input v-model="registerData.confirmarPassword" :label="'Senha'" :type="isPwd ? 'password' : 'text'" outlined dense >
              <template v-slot:prepend>
                <q-icon name="lock" size="15px" />
              </template>
              <template v-slot:append>
                <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" size="15px" />
              </template>
            </q-input>
          </q-form>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn
            push
            color="primary"
            text-color="white"
            :label="'Finalizar Cadastro'"
            no-caps
            @click="onClickSubmitRegister"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <q-dialog v-model="dialogSenha" transition-show="jump-down" transition-hide="jump-up">
      <q-card style="width: 70vw">
        <q-card-section>
          <div class="text-h6">Recuperação de Senha</div>
        </q-card-section>
        <q-card-section>
          <q-form @submit.prevent="onClickSubmitSenha" class="q-gutter-md" >
          <q-input v-model="senhaData.email" :label="'E-mail'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="mail" size="15px" />
            </template>
          </q-input>

          <q-input v-if="senhaData.mailSent" v-model="senhaData.code" :label="'Código'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="vpn_key" size="15px" />
            </template>
          </q-input>

          <q-input v-if="senhaData.mailSent" v-model="senhaData.password" :type="isPwd ? 'password' : 'text'" :label="'Nova Senha'" outlined dense >
            <template v-slot:prepend>
              <q-icon name="lock" size="15px" />
            </template>
          </q-input>
        </q-form>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          push
          color="primary"
          text-color="white"
          :label="senhaData.mailSent ? 'Confirmar nova Senha' : 'Enviar e-mail de recuperação'"
          no-caps
          @click="onClickSubmitSenha"
        />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-layout>
</template>

<script lang="ts">

import { defineComponent, onMounted, ref, watch, inject } from 'vue';
import EssentialLink from 'components/EssentialLink.vue';
import { useQuasar } from 'quasar';
import * as AuthService from 'src/services/AuthService'
import * as ChatService from 'src/services/ChatService'
import axios from 'axios';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { useAuthStore } from 'src/stores/auth';
import { useDarkStorage } from 'src/stores/dark';
import { UserType, useUserStore } from 'src/stores/user';
import { useRouter } from 'vue-router';
import { formatDate, formatDateApenasData, formatDateChat } from 'src/utils/dates';
import { io, Socket } from 'socket.io-client';
import { environment } from 'src/environment/environment';
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'MainLayout',

  components: {
    EssentialLink
  },

  setup () {
    const $q = useQuasar()
    const bus = inject('bus');
    const router = useRouter()
    const leftDrawerOpen = ref(false)
    let dialogLogin = ref(false)
    let dialogChat = ref(false)
    let dialogRegister = ref(false)
    let dialogSenha = ref(false)
    const chatMessageInput = ref('')
    const chatMessages = ref([
      {
        collab: {
          fullname: '',
          id: ''
        },
        user: {
          fullname: '',
          id: ''
        },
        message: '',
        xata: {
          createdAt: '',
          stamp: ''
        }
      }
    ])
    const { t, locale } = useI18n()
    const position = ref(400)
    const auth = useAuthStore();
    const dark = useDarkStorage();
    const user = useUserStore();
    const isColaborador = ref(false)
    const formData = ref({ email: '', password: '' })
    const senhaData = ref({ email: '', cgc: '', password: '', code: '', mailSent: false })
    const registerData = ref({
      fullname: '',
      cellphone_number: '',
      email: '',
      password: '',
      confirmarPassword: '',
     })
    const isPwd = ref(true)
    const isLogged = ref(false)
    const backendUp = ref(false)
    const socketConnected = ref<boolean>(false)
    const User = ref<UserType>()
    const SocketIo = ref<Socket>()

    const linksList = ref([
      {
        title: 'Página Inicial',
        caption: '',
        icon: 'home',
        link: '#/'
      }
    ])
    const linksLogged = ref([
      {
        title: 'Página Inicial',
        caption: '',
        icon: 'home',
        link: '#/'
      },
      {
        title: 'Perfil',
        caption: '',
        icon: 'person',
        link: '#/perfil'
      },
      {
        title: 'Solicitações',
        caption: '',
        icon: 'bolt',
        link: '#/solicitacoes'
      },
      {
        title: 'Biblioteca',
        caption: '',
        icon: 'person',
        link: '#/biblioteca'
      },
    ])
    const linksColaborador = ref([
      {
        title: 'Página Inicial',
        caption: '',
        icon: 'home',
        link: '#/'
      },
      {
        title: 'Perfil',
        caption: '',
        icon: 'person',
        link: '#/perfil'
      },
      {
        title: 'Solicitações',
        caption: '',
        icon: 'bolt',
        link: '#/solicitacoes_colaborador'
      },
      {
        title: 'Usuários',
        caption: '',
        icon: 'person',
        link: '#/usuarios'
      },
      {
        title: 'Dashboard',
        caption: '',
        icon: 'map',
        link: '#/dashboard'
      },
      {
        title: 'Biblioteca',
        caption: '',
        icon: 'book',
        link: '#/biblioteca'
      },
    ])

    const columns = [
      { name: 'item', align: 'left', label: 'Item', field: (row: { item: { data: any; nome: any; id: any; }; }) => { return row.item.data? `${row.item.nome} - ${formatDate(row.item.data)}`:row.item.nome; }, sortable: true },
      { name: 'id', align: 'right', label: 'id', field: 'id', sortable: true, visible: false },
      { name: 'quantidade', align: 'right', label: 'Quantidade', field: 'quantidade', sortable: true },
      { name: 'valor_total', align: 'right', label: 'Total', field: 'valor_total', sortable: true },
      { name: 'actions', label: ''}
    ]

    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
      isColaborador.value = newValue && (newValue.type == 'A' || newValue.type == 'C')
    })

    watch(() => User.value, (newValue: UserType) => {
      if (!newValue) return
      isColaborador.value = newValue && (newValue.type == 'A' || newValue.type == 'C')
      linksLogged.value = linksLogged.value.map((link) => {
        if (link.title === 'Perfil'){
          link.link = `#/perfil/${newValue.id}`
        }
        return link
      })
      linksColaborador.value = linksColaborador.value.map((link) => {
        if (link.title === 'Perfil'){
          link.link = `#/perfil/${newValue.id}`
        }
        return link
      })
    })

    const toggleLanguage = (lang: 'en' | 'pt') => {
      locale.value = lang
    }

    const getUser = async () => {
      User.value = await user.storeUserDataGetter
      backendUp.value = true

      if (!User.value.id) return

      const responseChat = await ChatService.GetMessages(User.value.id)
      chatMessages.value = []
      for (let message of responseChat.data){
        message.xata.stamp = await formatDateChat(message.xata.createdAt)
        chatMessages.value.push(message)
      }

      SocketIo.value = io(environment.baseUrl, {
        extraHeaders: {
          Authorization: `Bearer ${auth.storeAuthTokenGetter}`
        }
      })

      SocketIo.value.on('connect', () => {
        setTimeout(() => {
          socketConnected.value = true
        }, 1000);
      })

      SocketIo.value.on('chat', async (argument: any) => {
        if (!User.value) return
        if (!argument.collab.id) return
        chatMessages.value.push({
          user: {
            fullname: User.value.fullname,
            id: User.value.id
          },
          collab: {
            fullname: `${argument.collab.fullname}`,
            id: `${argument.collab.id}`
          },
          message: argument.message,
          xata: {
            createdAt: `${new Date()}`,
            stamp: `${await formatDateChat('')}`
          }
        })
      })
    }


    onMounted( async () => {
      backendUp.value = false
      bus.on('handleLogin', toggleDialogLogin);
      bus.on('handleIsLogged', handleIsLogged);
      await getUser()
      try {
        const darkData = await dark.storeDarkGetter
        if (darkData != null){
          $q.dark.set(darkData)
        }
        const authData = await auth.storeAuthTokenGetter
        const validate = await AuthService.Validate({ token: authData })
        if (validate.data?.success){
          isLogged.value = true
          const usuario = await user.storeUserDataGetter
          User.value = usuario
        } else {
          await auth.storageAuthTokenRemove
          await user.storageUserRemove
        }
      } catch (error) {
        if (!axios.isAxiosError(error)) console.log('AAA ' + error)
        await auth.storageAuthTokenRemove
        await user.storageUserRemove
      }
    })

    const onClickSendMessage = async () => {
      if (!User.value) return
      SocketIo.value?.emit('chat', {
        usuario: {
          id: User.value.id
        },
        collab: {
          id: null
        },
        message: chatMessageInput.value
      })

      chatMessages.value.push({
        collab: null,
        user: {
          fullname: `${User.value?.fullname}`,
          id: `${User.value?.id}`
        },
        message: chatMessageInput.value,
        xata: {
          createdAt: `${new Date()}`,
          stamp: `${await formatDateChat('')}`
        }
      })

      chatMessageInput.value = ''
      // try {
      //   const response = await AuthService.Login({...formData.value})
      //   if (axios.isAxiosError(response)) {
      //     if (response.response){
      //       triggerNegative(response.response.data.message);
      //     } else {
      //       triggerNegative('Houve um erro ao fazer o Login');
      //     }
      //     return
      //   }
      //   isLogged.value = true
      //   dialogLogin.value = false

      //   formData.value = { email: '', password: '' }
      // } catch (error) {
      //   console.log(error)
      //   throw error;
      // } finally {
      //   $q.loading.hide();
      // }
    };

    const onSubmit = async () => {
      $q.loading.show();
      try {
        const response = await AuthService.Login({...formData.value})
        if (axios.isAxiosError(response)) {
          if (response.response){
            triggerNegative(response.response.data.message);
          } else {
            triggerNegative('Houve um erro ao fazer o Login');
          }
          return
        }
        isLogged.value = true
        dialogLogin.value = false

        formData.value = { email: '', password: '' }
      } catch (error) {
        console.log(error)
        throw error;
      } finally {
        $q.loading.hide();
      }
    };

    const onClickLoginGoogle = async () => {
      $q.loading.show();
      try {
        window.open(environment.baseUrl + '/auth/google', '_self')
      } catch (error) {
        console.log(error)
        throw error;
      } finally {
        $q.loading.hide();
      }
    };



    const onClickSubmitRegister = async () => {

      if (registerData.value.password != registerData.value.confirmarPassword) {
        $q.loading.hide();
        triggerNegative('Senhas não coincidem');
      }
      $q.loading.show();
      try {
        const response = await AuthService.Register({...registerData.value})
        if (axios.isAxiosError(response)) {
          if (response.response){
            triggerNegative(response.response.data.message);
          } else {
            triggerNegative('Houve um erro ao fazer o Cadastro');
          }
          return
        }

        triggerSuccess('Conta criada com sucesso!');
        dialogRegister.value = false
      } catch (error) {
        console.log(error)
        throw error;
      } finally {
        $q.loading.hide();
      }

    };

    const onClickSubmitSenha = async () => {
      $q.loading.show();
      try {
        if (!senhaData.value.mailSent){
          const response = await AuthService.ForgetPassword({...senhaData.value})
          if (axios.isAxiosError(response)) {
            if (response.response){
              triggerNegative(response.response.data.message);
            } else {
              triggerNegative('Houve um erro ao enviar email de recuperação');
            }
            return
          }
          senhaData.value.mailSent = true
          triggerSuccess('E-mail enviado com sucesso!')
        } else {
          const response = await AuthService.ResetPassword({...senhaData.value})
          if (axios.isAxiosError(response)) {
            if (response.response){
              triggerNegative(response.response.data.message);
            } else {
              triggerNegative('Houve um erro ao confirmar senha');
            }
            return
          }

          senhaData.value.mailSent = false
          dialogSenha.value = false

          triggerSuccess('Senha alterada com sucesso.')
        }
      } catch (error) {
        console.log(error)
        senhaData.value.mailSent = false
        throw error;
      } finally {
        $q.loading.hide();
      }

    };

    const onClickLogout = async () => {
      AuthService.signOut();
      isLogged.value = false
      const usuario = await user.storeUserDataGetter
      User.value = usuario
      router.push('/')
    };

    const onClickRegister = async () => {
      dialogLogin.value = false
      dialogRegister.value = true
    };

    const onClickSenha = async () => {
      dialogLogin.value = false
      dialogSenha.value = true
    };

    const toggleDialogLogin = async () => {
      dialogLogin.value = !dialogLogin.value
    }

    const handleIsLogged = async () => {
      isLogged.value = true
      triggerSuccess('Logado com sucesso!')
    }

    return {
      linksLogged,
      linksColaborador,
      linksList,
      formData,
      registerData,
      senhaData,
      leftDrawerOpen,
      dialogLogin,
      dialogChat,
      dialogRegister,
      dialogSenha,
      isPwd,
      isLogged,
      isColaborador,
      User,
      onSubmit,
      onClickLoginGoogle,
      onClickLogout,
      onClickRegister,
      onClickSenha,
      onClickSubmitRegister,
      onClickSubmitSenha,
      formatDate,
      formatDateApenasData,
      columns,
      backendUp,
      chatMessageInput,
      chatMessages,
      position,
      onClickSendMessage,
      socketConnected,
      router,
      toggleLanguage,
      locale,
      toggleLeftDrawer () {
        leftDrawerOpen.value = !leftDrawerOpen.value
      },
      toggleDialogLogin,
      toggledialogRegister () {
        dialogRegister.value = !dialogRegister.value
      },
      toggledialogSenha () {
        dialogSenha.value = !dialogSenha.value
      },
      async toggleDarkMode () {
        $q.dark.toggle()
        await dark.storageDarkSave($q.dark.isActive)
      },
      toggleDialogChat () {
        dialogChat.value = !dialogChat.value
      },
    }
  }
});
</script>

<style lang="scss">
  .login-form-container {
  width: 320px;
  border-radius: 0.75rem;
  background-color: rgba(17, 24, 39, 1);
  padding: 2rem;
  color: rgba(243, 244, 246, 1);
}

.login-title {
  text-align: center;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
}

.login-form {
  margin-top: 1.5rem;
}

.login-input-group {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.login-input-group label {
  display: block;
  color: rgba(156, 163, 175, 1);
  margin-bottom: 4px;
}

.login-input-group input {
  border-radius: 0.375rem;
  /* border: 1px solid rgba(55, 65, 81, 1); */
  outline: 0;
  padding: 0.75rem 1rem;
}

.login-input-group input:focus {
  border-color: rgba(167, 139, 250);
}

.login-forgot {
  display: flex;
  justify-content: flex-end;
  font-size: 0.75rem;
  line-height: 1rem;
  margin: 8px 0 14px 0;
}

.login-forgot a, .login-signup a {
  color: rgba(243, 244, 246, 1);
  text-decoration: none;
  font-size: 14px;
}

.login-forgot a:hover, .login-signup a:hover {
  text-decoration: underline rgba(167, 139, 250, 1);
}

.login-sign {
  display: block;
  width: 100%;
  background-color: $primary;
  padding: 0.75rem;
  text-align: center;
  color: rgb(255, 255, 255);
  border: none;
  border-radius: 0.375rem;
  font-weight: 600;
}

.login-signup {
  text-align: center;
  font-size: 0.75rem;
  line-height: 1rem;
  color: rgba(156, 163, 175, 1);
}

.btn {
  margin-top: 10px;
  width: 100%;
  height: 50px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  gap: 10px;
  border: 1px solid #ededef;
  background-color: white;
  cursor: pointer;
  transition: 0.2s ease-in-out;
}

.btn:hover {
  border: 1px solid #2d79f3;
}

.login-social-message {
  display: flex;
  align-items: center;
  padding-top: 1rem;
}

.login-line {
  height: 1px;
  flex: 1 1 0%;
  background-color: rgba(55, 65, 81, 1);
}

.login-social-message .login-message {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgba(156, 163, 175, 1);
}

.hero-section {
  background-image: url('path-to-your-background-image.jpg');
  background-size: cover;
  background-position: center;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  scroll-margin-top: 100px; /* altura do header */
}

.custom-shape-divider-top-1742139876 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    transform: rotate(180deg);
}

.custom-shape-divider-top-1742139876 svg {
    position: relative;
    display: block;
    width: calc(145% + 1.3px);
    height: 63px;
}

.custom-shape-divider-top-1742139876 .shape-fill {
    fill: #FFFFFF;
}

.custom-shape-divider-bottom-1742183570 {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    transform: rotate(180deg);
}

.custom-shape-divider-bottom-1742183570 svg {
    position: relative;
    display: block;
    width: calc(174% + 1.3px);
    height: 150px;
}

.custom-shape-divider-bottom-1742183570 .shape-fill {
    fill: #FFFFFF;
}

.custom-shape-divider-bottom-1742234601 {
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    transform: rotate(180deg);
}

.custom-shape-divider-bottom-1742234601 svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 76px;
}

.custom-shape-divider-bottom-1742234601 .shape-fill {
    fill: #FFFFFF;
}

</style>