import { defineStore } from 'pinia';
import { GEO_STORAGE } from 'src/stores/storageConfig';

export type StorageGeoProps = {
  coords: {
    latitude: number,
    longitude: number,
  },
  timestamp: number
};

const defaultData = {
  coords: {
    latitude: 0,
    longitude: 0,
  },
  timestamp: 0
};

const getGeoStorage = () => {
  const response = localStorage.getItem(GEO_STORAGE);
  const data: StorageGeoProps = response
    ? JSON.parse(response)
    : defaultData;
  return data;
};

export const useGeoStore = defineStore(GEO_STORAGE, {
  state: () => ({
    geoData: getGeoStorage(),
  }),
  getters: {
    storeGeoGetter(state) {
      return state.geoData
    },
  },
  actions: {
    storageGeoSave(newGeoData: any) {
      this.geoData = newGeoData;
      const newPosition: StorageGeoProps = {
        coords: {
          latitude: newGeoData.coords.latitude,
          longitude: newGeoData.coords.longitude,
        },
        timestamp: newGeoData.timestamp
      }
      localStorage.setItem(GEO_STORAGE, JSON.stringify(newPosition));
    },
    storageGeoRemove() {
      this.geoData = defaultData;
      localStorage.removeItem(GEO_STORAGE);
    },
  },
});
