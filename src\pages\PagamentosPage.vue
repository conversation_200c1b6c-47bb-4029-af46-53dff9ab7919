<template>
  <q-page class="w-full h-full" :class="$q.dark.isActive ? 'bg-inherit' : 'bg-purple-100'">

    <div class="p-3 gap-3">
      <div class="row mt-2 w-full p-[0px] sm:p-[15px]">
        <p class="col text-lg font-league font-bold">Pagamentos</p>
      </div>

      <div class="w-full p-[0px] sm:p-[15px]">
        <q-table
          :rows="Pagamentos"
          :columns="columns"
          row-key="name"
          :pagination="{rowsPerPage: 50}"
        >
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="endDate">
                {{ formatDateApenasData(props.row.endDate) }}
              </q-td>
              <q-td key="name" class="text-left">
                {{ props.row.name }}
              </q-td>
              <q-td key="status" class="text-center">
                <q-badge v-if="props.row.status === 'RECEIVED'" class="p-2" rounded color="green" label="Recebido" />
                <q-badge v-if="props.row.status === 'CONFIRMED'" class="p-2" rounded color="blue" label="Confirmado" />
                <q-badge v-if="props.row.status === 'PENDING'" class="p-2" rounded color="yellow" text-color="black" label="Pendente" />
                <q-badge v-if="props.row.status === 'CANCELADO'" class="p-2" rounded color="red" label="Cancelada" />
                <q-badge v-if="props.row.status === 'ATRASO'" class="p-2" rounded color="red" label="Em atraso" />
                <q-badge v-if="props.row.status === 'EXCLUIDO'" class="p-2" rounded color="red" label="Excluído" />
              </q-td>
              <q-td key="actions" class="text-center">
                <q-btn rounded color="primary" label="Link de Pagamento" :href="props.row.url" target="_blank" />
              </q-td>
            </q-tr>
          </template>
          <template v-slot:no-data="">
            <div class="full-width row flex-center q-gutter-sm">
              <q-icon size="2em" name="sentiment_dissatisfied" />
              <span>
                Essa solicitação ainda não tem pagamnentos vinculados
              </span>
            </div>
          </template>
        </q-table>
      </div>
    </div>
  </q-page>
</template>

<script lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { UserType, useUserStore } from 'src/stores/user';
import { defineComponent, onMounted, ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import * as PagamentoService from 'src/services/PagamentoService';
import { triggerNegative, triggerSuccess } from 'src/utils/triggers';
import { formatDate, formatDateApenasData } from 'src/utils/dates';
// import axios from 'axios';

export default defineComponent({
  name: 'PagamentosPage',
  components: {  },
  setup () {
    const $q = useQuasar()
    const user = useUserStore();
    const User = ref<UserType>()
    const route = useRoute()
    const router = useRouter()
    const id = ref<string>()
    const Pagamentos = ref([{ name: '' }])
    
    const columns = ref([
      { name: 'endDate', label: 'Vencimento', align: 'left', field: 'endDate', sortable: true },
      { name: 'name', label: 'Título', align: 'left', field: 'name', sortable: true },
      { name: 'status', label: 'Status', align: 'center', field: 'status', sortable: true },
      { name: 'url', label: 'URL', align: 'left', field: 'url', sortable: false },
      // { name: 'actions', label: 'Ações', align: 'right', field: 'actions', sortable: false },
    ])
    
    watch(() => user.userData, (newValue: UserType) => {
      User.value = newValue
    })

    const getPagamentos = async () => {
      $q.loading.show({ message: 'Buscando pagamentos' })
      const response = await PagamentoService.GetOne(`${id.value}`)
      if (response.data){
        Pagamentos.value = response.data
      } else {
        triggerNegative('Houve um erro ao buscar as solicitações')
      }
      $q.loading.hide()
    }

    onMounted( async () => {
      const idRoute = route.params.id
      if (typeof(idRoute) === 'string') {
        if (idRoute != 'create'){
          id.value = idRoute
          getPagamentos()
        }
      }
    })

    return { 
      User,
      formatDate,
      formatDateApenasData,
      Pagamentos,
      columns,
      // router
     };


  }
});
</script>

<style scoped>

</style>