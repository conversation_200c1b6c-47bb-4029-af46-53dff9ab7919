import { instance } from 'src/services/Interceptor/interceptor';
import { environment } from 'src/environment/environment';
import { Login, Validate, Register, Password } from '../types/interfaces/IAuth'
import { StorageAuthTokenProps, useAuthStore } from 'src/stores/auth';
import { useGeoStore } from 'src/stores/geo';
import { UserType, useUserStore } from 'src/stores/user';
import { Geolocation } from '@capacitor/geolocation'
import { GEO_STORAGE } from 'src/stores/storageConfig';

function updateAuthStore(authentication: StorageAuthTokenProps){
  const auth = useAuthStore()
  auth.storageAuthTokenSave(authentication)
}

function updateUserStore(userProps: UserType){
  const user = useUserStore()
  user.storageUserSave(userProps)
}

export async function Login(data: Login) {
  const response = await instance.post(`${environment.baseUrl}/user/auth`, data);
  if (response.data){
    if (response.data.usuario){
      updateAuthStore({
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        token: response.data.token,
        refreshToken: '',
        gender: ''
      });
    
      updateUserStore({
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        type: response.data.usuario.type
      })
    }
  }
  return response
}

export function Validate(data: Validate) {
  Geolocation.getCurrentPosition().then(newPosition => {
    const geo = useGeoStore()
    geo.storageGeoSave(newPosition)
  })

  let newData = data

  const stringGeoData = localStorage.getItem(GEO_STORAGE)
  if (stringGeoData){
    const geoData = JSON.parse(stringGeoData)
    newData = {...data, ...geoData}
  }
  
  return instance.post(`${environment.baseUrl}/user/validate`, newData);
}

export async function ValidateToken(token: string) {
  const response = await instance.post(`${environment.baseUrl}/user/validate-token`, {token});
  if (response.data){
    if (response.data.usuario){
      updateAuthStore({
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        token: response.data.usuario.token,
        refreshToken: '',
        gender: ''
      });
    
      updateUserStore({
        id: response.data.usuario.id,
        email: response.data.usuario.email,
        cgc: response.data.usuario.cgc,
        fullname: response.data.usuario.fullname,
        cellphone_number: response.data.usuario.cellphone_number,
        type: response.data.usuario.type
      })
    }
  }
  return response
}


export function Register(data: Register) {
  return instance.post(`${environment.baseUrl}/user`, data);
}

export function ForgetPassword(data: Password) {
  return instance.post(`${environment.baseUrl}/user/password/reset/request`, data);
}

export function ResetPassword(data: Password) {
  return instance.post(`${environment.baseUrl}/user/password/reset`, data);
}

export function signOut() {
  const auth = useAuthStore();
  const user = useUserStore();

  auth.storageAuthTokenRemove();
  user.storageUserRemove();
}